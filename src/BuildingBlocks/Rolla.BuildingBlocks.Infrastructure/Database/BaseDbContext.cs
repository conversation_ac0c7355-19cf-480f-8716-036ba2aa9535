using Microsoft.EntityFrameworkCore;
using Rolla.Shared.Kernel.Domain;

namespace Rolla.BuildingBlocks.Infrastructure.Database;

public abstract class BaseDbContext(DbContextOptions options) : DbContext(options)
{
    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        UpdateAuditableEntities();
        return await base.SaveChangesAsync(cancellationToken);
    }

    private void UpdateAuditableEntities()
    {
        var entries = ChangeTracker
            .Entries<IAuditable>()
            .Where(e => new[] { EntityState.Added, EntityState.Modified }.Contains(e.State));

        foreach (var entityEntry in entries)
        {
            if (entityEntry.State == EntityState.Added)
            {
                entityEntry.Entity.CreatedAt = DateTime.UtcNow;
            }
            else
            {
                entityEntry.Entity.UpdatedAt = DateTime.UtcNow;
            }
        }
    }
}

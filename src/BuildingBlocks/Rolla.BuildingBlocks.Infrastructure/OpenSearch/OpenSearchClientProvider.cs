using Microsoft.Extensions.Options;
using OpenSearch.Client;
using Rolla.BuildingBlocks.Application.Interfaces;

namespace Rolla.BuildingBlocks.Infrastructure.OpenSearch;

public class OpenSearchClientProvider : IOpenSearchClientProvider
{
    private readonly IOpenSearchClient _client;
    private readonly OpenSearchOptions _options;

    public OpenSearchClientProvider(IOptions<OpenSearchOptions> options)
    {
        _options = options.Value;

        if (string.IsNullOrEmpty(_options.Host))
        {
            throw new InvalidOperationException("OpenSearch host is not configured.");
        }

        var uri = new Uri(_options.Host);
        var connectionSettings = new ConnectionSettings(uri)
            .RequestTimeout(TimeSpan.FromSeconds(_options.ConnectionTimeoutSeconds))
            .MaximumRetries(_options.MaxRetryAttempts);

        if (_options.EnableDebugMode ||
            string.Equals(_options.Environment, "qa", StringComparison.OrdinalIgnoreCase))
        {
            connectionSettings
                .EnableDebugMode()
                .Pretty<PERSON>son()
                .DisableDirectStreaming();
        }

        _client = new OpenSearchClient(connectionSettings);
    }

    public IOpenSearchClient GetClient() => _client;
}

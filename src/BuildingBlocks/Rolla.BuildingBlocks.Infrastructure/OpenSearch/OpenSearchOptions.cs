namespace Rolla.BuildingBlocks.Infrastructure.OpenSearch;

public class OpenSearchOptions
{
    public const string SectionName = "OpenSearch";

    public string Host { get; set; } = string.Empty;
    public int MaxRetryAttempts { get; set; } = 3;
    public int ConnectionTimeoutSeconds { get; set; } = 30;
    public bool EnableDebugMode { get; set; }
    public string Environment { get; set; } = "production";
}

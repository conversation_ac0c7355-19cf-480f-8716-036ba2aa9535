using Amazon;
using Amazon.SQS;
using Amazon.SQS.Model;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Rolla.BuildingBlocks.Application.Interfaces;
using System.Text.Json;

namespace Rolla.BuildingBlocks.Infrastructure.SQS;

public class SqsClientProvider : ISqsClient
{
    private readonly IAmazonSQS _sqsClient;
    private readonly ILogger<SqsClientProvider> _logger;
    private readonly SqsOptions _options;
    private readonly Dictionary<string, string> _queueUrlCache = new();

    public SqsClientProvider(
        IOptions<SqsOptions> options,
        ILogger<SqsClientProvider> logger)
    {
        _options = options.Value;
        _logger = logger;

        if (string.IsNullOrWhiteSpace(_options.Region))
        {
            throw new InvalidOperationException("SQS Region is not configured.");
        }

        var config = new AmazonSQSConfig
        {
            RegionEndpoint = RegionEndpoint.GetBySystemName(_options.Region)
        };

        _sqsClient = new AmazonSQSClient(config);
    }

    public async Task<bool> SendMessageAsync<T>(string queueName, T message, string? messageType = null)
    {
        try
        {
            var queueUrl = await GetQueueUrlAsync(queueName);

            var request = new SendMessageRequest
            {
                QueueUrl = queueUrl,
                MessageBody = JsonSerializer.Serialize(message)
            };

            if (!string.IsNullOrEmpty(messageType))
            {
                request.MessageAttributes = new Dictionary<string, MessageAttributeValue>
                {
                    {
                        "MessageType",
                        new MessageAttributeValue
                        {
                            DataType = "String",
                            StringValue = messageType
                        }
                    }
                };
            }

            var response = await _sqsClient.SendMessageAsync(request);

            return response.HttpStatusCode == System.Net.HttpStatusCode.OK;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending message to SQS queue {QueueName}: {Message}", queueName, ex.Message);
            return false;
        }
    }

    private async Task<string> GetQueueUrlAsync(string queueName)
    {
        if (_queueUrlCache.TryGetValue(queueName, out var cachedUrl))
        {
            return cachedUrl;
        }

        var fullQueueName = $"{_options.Environment}-{queueName}-topic";

        try
        {
            var response = await _sqsClient.GetQueueUrlAsync(fullQueueName);
            _queueUrlCache[queueName] = response.QueueUrl;

            return response.QueueUrl;
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Failed to get URL for queue '{fullQueueName}': {ex.Message}", ex);
        }
    }
}

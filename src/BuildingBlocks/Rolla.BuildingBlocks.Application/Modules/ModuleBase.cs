using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Rolla.BuildingBlocks.Application.Modules;

public abstract class ModuleBase : IModule
{
    public abstract void RegisterModule(IServiceCollection services, IConfiguration configuration);

    public virtual void UseModule(IApplicationBuilder app)
    {
        // Default implementation - can be overridden
    }
}

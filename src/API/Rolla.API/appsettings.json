{"Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}}, "ConnectionStrings": {"DefaultConnection": "Server=qa-rolla-main-rds.cji8wsokc2ii.eu-central-1.rds.amazonaws.com;Database=rolla_test;User=rolla_master;Password=****************;"}, "AWS": {"Region": "eu-central-1", "AccessKeyId": "********************", "SecretAccessKey": "gSF0/Is1A6mpi+YmQ8oTGIIGqb1Sc6RYEkqcibsY"}, "OpenSearch": {"Host": "https://vpc-qa-rolla-opensearch-ygzax3iz46eg66dgzepkctcob4.eu-central-1.es.amazonaws.com/", "MaxRetryAttempts": 3, "ConnectionTimeoutSeconds": 30, "EnableDebugMode": false, "Environment": "qa"}, "Insights": {"OpenAIApiKey": "********************************************************************************************************************************************************************", "OpenAIModel": "gpt-4", "TimeoutSeconds": 30}, "AllowedHosts": "*"}
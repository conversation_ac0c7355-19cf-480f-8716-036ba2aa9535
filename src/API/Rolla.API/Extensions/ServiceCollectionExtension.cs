using Rolla.BuildingBlocks.Application.Interfaces;
using Rolla.BuildingBlocks.Application.Modules;
using Rolla.BuildingBlocks.Infrastructure.OpenSearch;
using Rolla.Modules.Health.Api.Module;
using Rolla.Modules.Insights.Api.Module;
using Rolla.Modules.RollaAdmin.Api.Module;

namespace Rolla.API.Extensions;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddInsightsModule(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        IModule module = new InsightsModule();
        module.RegisterModule(services, configuration);
        return services;
    }

    public static IServiceCollection AddHealthModule(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        IModule module = new HealthModule();
        module.RegisterModule(services, configuration);
        return services;
    }

    public static IServiceCollection AddOpenSearch(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        services.Configure<OpenSearchOptions>(
            configuration.GetSection(OpenSearchOptions.SectionName));

        services.AddSingleton<IOpenSearchClientProvider, OpenSearchClientProvider>();

        return services;
    }

    public static IServiceCollection AddRollaAdminModule(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        IModule module = new RollaAdminModule();
        module.RegisterModule(services, configuration);
        return services;
    }
}

public static class ApplicationBuilderExtensions
{
    public static WebApplication UseInsightsModule(this WebApplication app)
    {
        IModule module = new InsightsModule();
        module.UseModule(app);
        return app;
    }

    public static WebApplication UseHealthModule(this WebApplication app)
    {
        IModule module = new HealthModule();
        module.UseModule(app);
        return app;
    }

    public static WebApplication UseRollaAdminModule(this WebApplication app)
    {
        IModule module = new RollaAdminModule();
        module.UseModule(app);
        return app;
    }
}

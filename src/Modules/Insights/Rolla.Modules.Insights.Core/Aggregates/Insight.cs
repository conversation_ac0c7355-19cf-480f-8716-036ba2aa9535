using Rolla.Modules.Insights.Core.ValueObjects;
using Rolla.Shared.Kernel.Domain;

namespace Rolla.Modules.Insights.Core.Aggregates;

public class Insight : AggregateRoot
{
    public int UserId { get; private set; }
    public int? PromptId { get; private set; }
    public string TriggerType { get; private set; }
    public string Content { get; private set; }
    public InsightStatus Status { get; private set; }
    public DateTime? GeneratedAt { get; private set; }
    public DateTime? ViewedAt { get; private set; }
    public DateTime? FailedAt { get; private set; }
    public string? FailureReason { get; private set; }
    public string? HealthDataSnapshot { get; private set; }
    public UserFeedback? Feedback { get; private set; }

    private Insight()
    {
        TriggerType = string.Empty;
        Content = string.Empty;
        Status = InsightStatus.Pending;
    }

    public static Insight Create(int userId, string triggerType, int? promptId = null)
    {
        var insight = new Insight
        {
            UserId = userId,
            TriggerType = triggerType ?? throw new ArgumentNullException(nameof(triggerType)),
            PromptId = promptId,
            Status = InsightStatus.Pending,
            CreatedAt = DateTime.UtcNow
        };

        return insight;
    }

    public void StartGenerating(string healthDataSnapshot)
    {
        if (Status != InsightStatus.Pending)
            throw new InvalidOperationException($"Cannot start generating insight in {Status.Value} status");

        HealthDataSnapshot = healthDataSnapshot;
        Status = InsightStatus.Generating;
    }

    public void MarkAsGenerated(string content)
    {
        if (Status != InsightStatus.Generating)
            throw new InvalidOperationException($"Cannot mark insight as generated in {Status.Value} status");

        Content = content ?? throw new ArgumentNullException(nameof(content));
        Status = InsightStatus.Generated;
        GeneratedAt = DateTime.UtcNow;
    }

    public void MarkAsFailed(string reason)
    {
        Status = InsightStatus.Failed;
        FailedAt = DateTime.UtcNow;
        FailureReason = reason;
    }

    public void MarkAsViewed()
    {
        if (Status != InsightStatus.Generated)
            return;

        Status = InsightStatus.Viewed;
        ViewedAt = DateTime.UtcNow;
    }

    public void AddFeedback(bool isPositive)
    {
        Feedback = isPositive ? UserFeedback.Positive : UserFeedback.Negative;
    }
}

public enum UserFeedback
{
    Positive,
    Negative
}

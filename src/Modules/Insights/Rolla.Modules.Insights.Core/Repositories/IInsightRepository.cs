using Rolla.Modules.Insights.Core.Aggregates;

namespace Rolla.Modules.Insights.Core.Repositories;

public interface IInsightRepository
{
    Task<Insight?> GetByIdAsync(int id, CancellationToken cancellationToken);

    Task<List<Insight>> GetUserInsightsAsync(int userId, DateTime from, DateTime to,
        CancellationToken cancellationToken);

    Task<int> CountUserInsightsTodayAsync(int userId, string? triggerType, CancellationToken cancellationToken);
    Task AddAsync(Insight insight, CancellationToken cancellationToken);
    Task UpdateAsync(Insight insight, CancellationToken cancellationToken);
}

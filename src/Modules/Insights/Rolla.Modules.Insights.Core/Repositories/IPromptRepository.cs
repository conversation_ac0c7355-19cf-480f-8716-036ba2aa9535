using Rolla.Modules.Insights.Core.Entities;

namespace Rolla.Modules.Insights.Core.Repositories;

public interface IPromptRepository
{
    Task<Prompt?> GetByIdAsync(int id, CancellationToken cancellationToken);
    Task<List<Prompt>> GetActivePromptsByTriggerAsync(string triggerType, CancellationToken cancellationToken);
    Task<Prompt?> GetRandomPromptForTriggerAsync(string triggerType, CancellationToken cancellationToken);
    Task UpdateAsync(Prompt prompt, CancellationToken cancellationToken);
}

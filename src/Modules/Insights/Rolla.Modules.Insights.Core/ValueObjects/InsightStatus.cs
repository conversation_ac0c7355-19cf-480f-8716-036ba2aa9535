using Rolla.Shared.Kernel.Domain;

namespace Rolla.Modules.Insights.Core.ValueObjects;

public class InsightStatus : ValueObject
{
    public string Value { get; }

    public static InsightStatus Pending => new("Pending");
    public static InsightStatus Generating => new("Generating");
    public static InsightStatus Generated => new("Generated");
    public static InsightStatus Failed => new("Failed");
    public static InsightStatus Viewed => new("Viewed");

    public InsightStatus(string value)
    {
        Value = value;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Value;
    }
}

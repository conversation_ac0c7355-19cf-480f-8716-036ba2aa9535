using Rolla.Shared.Kernel.Domain;

namespace Rolla.Modules.Insights.Core.ValueObjects;

public class TriggerType : ValueObject
{
    public string Value { get; }

    public static TriggerType Morning => new("Morning");
    public static TriggerType Midday => new("Midday");
    public static TriggerType Evening => new("Evening");
    public static TriggerType TodaysMaxHS => new("TodaysMaxHS");
    public static TriggerType ActivityCompleted => new("ActivityCompleted");
    public static TriggerType HS80 => new("HS80");
    public static TriggerType ActivityGoal50 => new("50%ActGoal");

    public TriggerType(string value)
    {
        if (string.IsNullOrWhiteSpace(value))
            throw new ArgumentException("Trigger type cannot be empty", nameof(value));

        Value = value;
    }

    public bool IsTimeBased()
    {
        return Value == Morning.Value || Value == Midday.Value || Value == Evening.Value;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Value;
    }
}

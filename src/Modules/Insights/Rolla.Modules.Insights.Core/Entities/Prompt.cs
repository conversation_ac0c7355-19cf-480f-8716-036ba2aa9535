using Rolla.Shared.Kernel.Domain;

namespace Rolla.Modules.Insights.Core.Entities;

public class Prompt : Entity
{
    private readonly List<string> _triggers = new();
    private readonly List<string> _tags = new();

    public string Title { get; private set; }
    public string PromptText { get; private set; }
    public IReadOnlyList<string> Triggers => _triggers.AsReadOnly();
    public IReadOnlyList<string> Tags => _tags.AsReadOnly();
    public string Position { get; private set; }
    public PromptStatus Status { get; private set; }
    public int LikeCount { get; private set; }
    public int DislikeCount { get; private set; }
    public int ImpressionCount { get; private set; }

    private Prompt()
    {
        Title = string.Empty;
        PromptText = string.Empty;
        Position = string.Empty;
    }

    public Prompt(string title, string promptText, string position)
    {
        Title = title ?? throw new ArgumentNullException(nameof(title));
        PromptText = promptText ?? throw new ArgumentNullException(nameof(promptText));
        Position = position ?? throw new ArgumentNullException(nameof(position));
        Status = PromptStatus.Draft;
        LikeCount = 0;
        DislikeCount = 0;
        ImpressionCount = 0;
    }

    public void AddTrigger(string trigger)
    {
        if (!_triggers.Contains(trigger))
            _triggers.Add(trigger);
    }

    public void AddTag(string tag)
    {
        if (!_tags.Contains(tag))
            _tags.Add(tag);
    }

    public void Activate()
    {
        Status = PromptStatus.Active;
    }

    public void Archive()
    {
        Status = PromptStatus.Archived;
    }

    public void IncrementImpression()
    {
        ImpressionCount++;
    }

    public void IncrementLike()
    {
        LikeCount++;
    }

    public void IncrementDislike()
    {
        DislikeCount++;
    }
}

public enum PromptStatus
{
    Draft,
    Active,
    Archived
}

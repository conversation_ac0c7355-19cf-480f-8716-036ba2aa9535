using Rolla.Modules.Insights.Core.ValueObjects;
using Rolla.Shared.Kernel.Domain;

namespace Rolla.Modules.Insights.Core.Entities;

public class Trigger : Entity
{
    public string Name { get; private set; }
    public TriggerType Type { get; private set; }
    public TimeSpan? StartTime { get; private set; }
    public TimeSpan? EndTime { get; private set; }
    public int MaxInsights { get; private set; }
    public int CooldownMinutes { get; private set; }
    public int Priority { get; private set; }
    public bool IncludedInDailyCap { get; private set; }
    public bool IsActive { get; private set; }

    private Trigger()
    {
        Name = string.Empty;
        Type = TriggerType.Evening;
    }

    public Trigger(
        string name,
        TriggerType type,
        int maxInsights,
        int cooldownMinutes,
        int priority,
        bool includedInDailyCap,
        TimeSpan? startTime = null,
        TimeSpan? endTime = null)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Type = type ?? throw new ArgumentNullException(nameof(type));
        MaxInsights = maxInsights;
        CooldownMinutes = cooldownMinutes;
        Priority = priority;
        IncludedInDailyCap = includedInDailyCap;
        StartTime = startTime;
        EndTime = endTime;
        IsActive = true;
    }

    public bool IsInTimeWindow(DateTime currentTime)
    {
        if (!Type.IsTimeBased() || StartTime == null || EndTime == null)
            return true;

        var currentTimeOfDay = currentTime.TimeOfDay;

        if (StartTime > EndTime) // Crosses midnight
        {
            return currentTimeOfDay >= StartTime || currentTimeOfDay <= EndTime;
        }

        return currentTimeOfDay >= StartTime && currentTimeOfDay <= EndTime;
    }

    public void Deactivate()
    {
        IsActive = false;
    }
}

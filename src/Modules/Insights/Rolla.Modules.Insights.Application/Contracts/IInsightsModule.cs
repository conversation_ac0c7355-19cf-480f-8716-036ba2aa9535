namespace Rolla.Modules.Insights.Application.Contracts;

public interface IInsightsModule
{
    Task<HealthDataDto> GetHealthDataAsync(int userId, CancellationToken cancellationToken = default);
}

public class HealthDataDto
{
    public SleepData? Sleep { get; set; }
    public MetricData? RHR { get; set; }
    public MetricData? HRV { get; set; }
    public MetricData? ActiveCalories { get; set; }
    public MetricData? Steps { get; set; }
    public ProfileData? Profile { get; set; }
}

public class SleepData
{
    public decimal? Duration { get; set; }
    public SleepPhases? Phases { get; set; }
    public decimal? Goal { get; set; }
}

public class SleepPhases
{
    public decimal? REM { get; set; }
    public decimal? Deep { get; set; }
    public decimal? Light { get; set; }
    public decimal? Awake { get; set; }
}

public class MetricData
{
    public decimal? Value { get; set; }
    public decimal? Goal { get; set; }
    public Dictionary<string, decimal?> StatisticsAvg { get; set; } = new();
}

public class ProfileData
{
    public string? Gender { get; set; }
    public int? Age { get; set; }
    public decimal? Weight { get; set; }
    public decimal? Height { get; set; }
}

using Microsoft.EntityFrameworkCore;
using Rolla.Modules.Insights.Core.Aggregates;
using Rolla.Modules.Insights.Core.Repositories;
using Rolla.Modules.Insights.Infrastructure.Database;

namespace Rolla.Modules.Insights.Infrastructure.Repositories;

public class InsightRepository : IInsightRepository
{
    private readonly InsightsDbContext _context;

    public InsightRepository(InsightsDbContext context)
    {
        _context = context;
    }

    public async Task<Insight?> GetByIdAsync(int id, CancellationToken cancellationToken)
    {
        return await _context.Insights
            .FirstOrDefaultAsync(i => i.Id == id, cancellationToken);
    }

    public async Task<List<Insight>> GetUserInsightsAsync(int userId, DateTime from, DateTime to,
        CancellationToken cancellationToken)
    {
        return await _context.Insights
            .Where(i => i.UserId == userId && i.CreatedAt >= from && i.CreatedAt <= to)
            .OrderByDescending(i => i.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<int> CountUserInsightsTodayAsync(int userId, string? triggerType,
        CancellationToken cancellationToken)
    {
        var today = DateTime.UtcNow.Date;
        var query = _context.Insights
            .Where(i => i.UserId == userId && i.CreatedAt >= today);

        if (!string.IsNullOrEmpty(triggerType))
        {
            query = query.Where(i => i.TriggerType == triggerType);
        }

        return await query.CountAsync(cancellationToken);
    }

    public async Task AddAsync(Insight insight, CancellationToken cancellationToken)
    {
        await _context.Insights.AddAsync(insight, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task UpdateAsync(Insight insight, CancellationToken cancellationToken)
    {
        _context.Insights.Update(insight);
        await _context.SaveChangesAsync(cancellationToken);
    }
}

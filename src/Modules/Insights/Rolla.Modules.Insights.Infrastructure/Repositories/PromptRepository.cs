using Microsoft.EntityFrameworkCore;
using Rolla.Modules.Insights.Core.Entities;
using Rolla.Modules.Insights.Core.Repositories;
using Rolla.Modules.Insights.Infrastructure.Database;

namespace Rolla.Modules.Insights.Infrastructure.Repositories;

public class PromptRepository : IPromptRepository
{
    private readonly InsightsDbContext _context;

    public PromptRepository(InsightsDbContext context)
    {
        _context = context;
    }

    public async Task<Prompt?> GetByIdAsync(int id, CancellationToken cancellationToken)
    {
        return await _context.Prompts
            .FirstOrDefaultAsync(p => p.Id == id, cancellationToken);
    }

    public async Task<List<Prompt>> GetActivePromptsByTriggerAsync(string triggerType,
        CancellationToken cancellationToken)
    {
        return await _context.Prompts
            .Where(p => p.Status == PromptStatus.Active && p.Triggers.Contains(triggerType))
            .ToListAsync(cancellationToken);
    }

    public async Task<Prompt?> GetRandomPromptForTriggerAsync(string triggerType, CancellationToken cancellationToken)
    {
        var prompts = await GetActivePromptsByTriggerAsync(triggerType, cancellationToken);

        if (!prompts.Any())
            return null;

        var random = new Random();
        return prompts[random.Next(prompts.Count)];
    }

    public async Task UpdateAsync(Prompt prompt, CancellationToken cancellationToken)
    {
        _context.Prompts.Update(prompt);
        await _context.SaveChangesAsync(cancellationToken);
    }
}

using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using OpenAI;
using OpenAI.Chat;
using Rolla.Modules.Insights.Application.Configuration;
using Rolla.Modules.Insights.Application.Services;

namespace Rolla.Modules.Insights.Infrastructure.External;

public class OpenAIService : ILLMService
{
    private readonly ChatClient _chatClient;
    private readonly InsightsOptions _options;
    private readonly ILogger<OpenAIService> _logger;

    public OpenAIService(
        IOptions<InsightsOptions> options,
        ILogger<OpenAIService> logger)
    {
        _options = options.Value;
        _logger = logger;

        var openAIClient = new OpenAIClient(_options.OpenAIApiKey);
        _chatClient = openAIClient.GetChatClient(_options.OpenAIModel);
    }

    public async Task<string> GenerateInsightAsync(
        string prompt,
        string healthDataJson,
        CancellationToken cancellationToken)
    {
        try
        {
            var systemPrompt = @"You are a health and fitness insights assistant. 
                Generate personalized, actionable insights based on user health data. 
                Keep insights concise (2-3 sentences), motivational, and specific to the data provided.";

            var userPrompt = $"{prompt}\n\nUser Health Data:\n{healthDataJson}";

            var messages = new List<ChatMessage>
            {
                new SystemChatMessage(systemPrompt),
                new UserChatMessage(userPrompt)
            };

            var chatOptions = new ChatCompletionOptions
            {
                Temperature = 0.7f,
                MaxOutputTokenCount = 150
            };

            using var cts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
            cts.CancelAfter(TimeSpan.FromSeconds(_options.TimeoutSeconds));

            var response = await _chatClient.CompleteChatAsync(messages, chatOptions, cts.Token);

            return response.Value.Content[0].Text ?? "Unable to generate insight at this time.";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate insight from OpenAI");
            throw;
        }
    }
}

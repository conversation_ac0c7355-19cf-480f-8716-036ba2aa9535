using Microsoft.EntityFrameworkCore;
using Rolla.BuildingBlocks.Infrastructure.Database;
using Rolla.Modules.Insights.Core.Aggregates;
using Rolla.Modules.Insights.Core.Entities;

namespace Rolla.Modules.Insights.Infrastructure.Database;

public class InsightsDbContext : BaseDbContext
{
    public DbSet<Insight> Insights { get; set; }
    public DbSet<Prompt> Prompts { get; set; }
    public DbSet<Trigger> Triggers { get; set; }

    public InsightsDbContext(DbContextOptions<InsightsDbContext> options) : base(options)
    {
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        modelBuilder.HasDefaultSchema("insights");

        // Configure Insight
        modelBuilder.Entity<Insight>(entity =>
        {
            entity.ToTable("insights");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.UserId).HasColumnName("user_id");
            entity.Property(e => e.PromptId).HasColumnName("prompt_id");
            entity.Property(e => e.TriggerType).HasColumnName("trigger_type").HasMaxLength(50);
            entity.Property(e => e.Content).HasColumnName("content");
            entity.Property(e => e.Status).HasColumnName("status")
                .HasConversion(
                    v => v.Value,
                    v => new Core.ValueObjects.InsightStatus(v));
            entity.Property(e => e.GeneratedAt).HasColumnName("generated_at");
            entity.Property(e => e.ViewedAt).HasColumnName("viewed_at");
            entity.Property(e => e.FailedAt).HasColumnName("failed_at");
            entity.Property(e => e.FailureReason).HasColumnName("failure_reason");
            entity.Property(e => e.HealthDataSnapshot).HasColumnName("health_data_snapshot");
            entity.Property(e => e.Feedback).HasColumnName("feedback");
            entity.Property(e => e.CreatedAt).HasColumnName("created_at");
            entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");
            entity.Property(e => e.Version).HasColumnName("version");

            entity.HasIndex(e => new { e.UserId, e.CreatedAt });
        });

        // Configure Prompt
        modelBuilder.Entity<Prompt>(entity =>
        {
            entity.ToTable("prompts");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Title).HasColumnName("title").HasMaxLength(200);
            entity.Property(e => e.PromptText).HasColumnName("prompt_text");
            entity.Property(e => e.Position).HasColumnName("position").HasMaxLength(50);
            entity.Property(e => e.Status).HasColumnName("status");
            entity.Property(e => e.LikeCount).HasColumnName("like_count");
            entity.Property(e => e.DislikeCount).HasColumnName("dislike_count");
            entity.Property(e => e.ImpressionCount).HasColumnName("impression_count");

            entity.Property(e => e.Triggers)
                .HasColumnName("triggers")
                .HasConversion(
                    v => string.Join(',', v),
                    v => v.Split(',', StringSplitOptions.RemoveEmptyEntries).ToList());

            entity.Property(e => e.Tags)
                .HasColumnName("tags")
                .HasConversion(
                    v => string.Join(',', v),
                    v => v.Split(',', StringSplitOptions.RemoveEmptyEntries).ToList());
        });

        // Configure Trigger
        modelBuilder.Entity<Trigger>(entity =>
        {
            entity.ToTable("triggers");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).HasColumnName("name").HasMaxLength(50);
            entity.Property(e => e.Type).HasColumnName("type")
                .HasConversion(
                    v => v.Value,
                    v => new Core.ValueObjects.TriggerType(v));
            entity.Property(e => e.StartTime).HasColumnName("start_time");
            entity.Property(e => e.EndTime).HasColumnName("end_time");
            entity.Property(e => e.MaxInsights).HasColumnName("max_insights");
            entity.Property(e => e.CooldownMinutes).HasColumnName("cooldown_minutes");
            entity.Property(e => e.Priority).HasColumnName("priority");
            entity.Property(e => e.IncludedInDailyCap).HasColumnName("included_in_daily_cap");
            entity.Property(e => e.IsActive).HasColumnName("is_active");
        });
    }
}

using Rolla.Modules.Insights.Application.Contracts;

namespace Rolla.Modules.Insights.Infrastructure.Services;

public class MockHealthDataService : IInsightsModule
{
    public Task<HealthDataDto> GetHealthDataAsync(int userId, CancellationToken cancellationToken = default)
    {
        // Mock implementation - replace with actual inter-module communication
        return Task.FromResult(new HealthDataDto
        {
            Sleep = new SleepData
            {
                Duration = 7.5m,
                Goal = 8m,
                Phases = new SleepPhases
                {
                    REM = 1.5m,
                    Deep = 1.8m,
                    Light = 3.7m,
                    Awake = 0.5m
                }
            },
            RHR = new MetricData
            {
                Value = 60,
                Goal = 65,
                StatisticsAvg = new Dictionary<string, decimal?>
                {
                    ["7D"] = 62,
                    ["30D"] = 63
                }
            },
            Steps = new MetricData
            {
                Value = 8500,
                Goal = 10000,
                StatisticsAvg = new Dictionary<string, decimal?>
                {
                    ["7D"] = 7800,
                    ["30D"] = 8200
                }
            }
        });
    }
}

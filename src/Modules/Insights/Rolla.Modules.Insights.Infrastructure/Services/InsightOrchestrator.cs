using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Rolla.Modules.Insights.Application.Configuration;
using Rolla.Modules.Insights.Application.Contracts;
using Rolla.Modules.Insights.Application.Services;
using Rolla.Modules.Insights.Core.Repositories;
using System.Text.Json;

namespace Rolla.Modules.Insights.Infrastructure.Services;

public class InsightOrchestrator : IInsightOrchestrator
{
    private readonly IInsightRepository _insightRepository;
    private readonly IPromptRepository _promptRepository;
    private readonly ILLMService _llmService;
    private readonly IInsightsModule _insightsModule;
    private readonly InsightsOptions _options;
    private readonly ILogger<InsightOrchestrator> _logger;

    public InsightOrchestrator(
        IInsightRepository insightRepository,
        IPromptRepository promptRepository,
        ILLMService llmService,
        IInsightsModule insightsModule,
        IOptions<InsightsOptions> options,
        ILogger<InsightOrchestrator> logger)
    {
        _insightRepository = insightRepository;
        _promptRepository = promptRepository;
        _llmService = llmService;
        _insightsModule = insightsModule;
        _options = options.Value;
        _logger = logger;
    }

    public async Task<bool> CanGenerateInsightAsync(
        int userId,
        string triggerType,
        CancellationToken cancellationToken)
    {
        // Check daily cap
        var todayCount = await _insightRepository.CountUserInsightsTodayAsync(
            userId, null, cancellationToken);

        if (todayCount >= _options.DailyInsightCap)
            return false;

        // Check trigger-specific limits
        var triggerCount = await _insightRepository.CountUserInsightsTodayAsync(
            userId, triggerType, cancellationToken);

        // TODO: Load trigger configuration and check limits
        // For now, allow all
        return true;
    }

    public async Task QueueInsightGenerationAsync(int insightId, CancellationToken cancellationToken)
    {
        // TODO: Implement SQS queuing
        // For now, process synchronously
        await GenerateInsightContentAsync(insightId, cancellationToken);
    }

    public async Task<string> GenerateInsightContentAsync(int insightId, CancellationToken cancellationToken)
    {
        try
        {
            var insight = await _insightRepository.GetByIdAsync(insightId, cancellationToken);
            if (insight == null)
                throw new InvalidOperationException($"Insight {insightId} not found");

            // Get health data
            var healthData = await _insightsModule.GetHealthDataAsync(insight.UserId, cancellationToken);
            var healthDataJson = JsonSerializer.Serialize(healthData);

            insight.StartGenerating(healthDataJson);
            await _insightRepository.UpdateAsync(insight, cancellationToken);

            // Get prompt
            var prompt = await _promptRepository.GetRandomPromptForTriggerAsync(
                insight.TriggerType, cancellationToken);

            if (prompt == null)
                throw new InvalidOperationException($"No active prompt found for trigger {insight.TriggerType}");

            // Generate content
            var content = await _llmService.GenerateInsightAsync(
                prompt.PromptText,
                healthDataJson,
                cancellationToken);

            // Update insight
            insight.MarkAsGenerated(content);
            await _insightRepository.UpdateAsync(insight, cancellationToken);

            // Update prompt statistics
            prompt.IncrementImpression();
            await _promptRepository.UpdateAsync(prompt, cancellationToken);

            return content;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate insight content for {InsightId}", insightId);

            var insight = await _insightRepository.GetByIdAsync(insightId, cancellationToken);
            if (insight != null)
            {
                insight.MarkAsFailed(ex.Message);
                await _insightRepository.UpdateAsync(insight, cancellationToken);
            }

            throw;
        }
    }
}

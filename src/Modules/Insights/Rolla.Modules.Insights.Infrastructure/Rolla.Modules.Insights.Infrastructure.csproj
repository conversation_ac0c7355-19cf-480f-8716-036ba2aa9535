<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.13"/>
        <PackageReference Include="Pomelo.EntityFrameworkCore.MySql" Version="8.0.3"/>
        <PackageReference Include="OpenAI" Version="2.1.0-beta.2"/>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Rolla.Modules.Insights.Core\Rolla.Modules.Insights.Core.csproj"/>
        <ProjectReference Include="..\Rolla.Modules.Insights.Application\Rolla.Modules.Insights.Application.csproj"/>
        <ProjectReference Include="..\..\..\BuildingBlocks\Rolla.BuildingBlocks.Infrastructure\Rolla.BuildingBlocks.Infrastructure.csproj"/>
    </ItemGroup>

</Project>
using Microsoft.AspNetCore.Builder;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Rolla.BuildingBlocks.Application.Modules;
using Rolla.Modules.Insights.Application.Configuration;
using Rolla.Modules.Insights.Application.Contracts;
using Rolla.Modules.Insights.Application.Services;
using Rolla.Modules.Insights.Core.Repositories;
using Rolla.Modules.Insights.Infrastructure.Database;
using Rolla.Modules.Insights.Infrastructure.External;
using Rolla.Modules.Insights.Infrastructure.Repositories;
using Rolla.Modules.Insights.Infrastructure.Services;

namespace Rolla.Modules.Insights.Api.Module;

public class InsightsModule : ModuleBase
{
    public override void RegisterModule(IServiceCollection services, IConfiguration configuration)
    {
        services.Configure<InsightsOptions>(configuration.GetSection(InsightsOptions.SectionName));

        services.AddDbContext<InsightsDbContext>(options =>
            options.UseMySql(
                configuration.GetConnectionString("DefaultConnection"),
                ServerVersion.AutoDetect(configuration.GetConnectionString("DefaultConnection"))));

        services.AddScoped<IInsightRepository, InsightRepository>();
        services.AddScoped<IPromptRepository, PromptRepository>();

        services.AddScoped<ILLMService, OpenAIService>();
        services.AddScoped<IInsightOrchestrator, InsightOrchestrator>();
        services.AddScoped<IInsightsModule, MockHealthDataService>();

    }

    public override void UseModule(IApplicationBuilder app)
    {
    }
}

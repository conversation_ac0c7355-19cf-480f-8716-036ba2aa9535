using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Rolla.Modules.Insights.Application.Contracts.Request;
using Rolla.Modules.Insights.Application.Contracts.Response;
using Rolla.Modules.Insights.Application.Services;
using Rolla.Modules.Insights.Core.Aggregates;
using Rolla.Modules.Insights.Core.Repositories;

namespace Rolla.Modules.Insights.Api.Controllers;

[ApiController]
[Route("api/insights")]
[ApiExplorerSettings(IgnoreApi = true)]
public class InsightsController : ControllerBase
{
    private readonly IInsightRepository _insightRepository;
    private readonly IPromptRepository _promptRepository;
    private readonly IInsightOrchestrator _orchestrator;
    private readonly ILogger<InsightsController> _logger;

    public InsightsController(
        IInsightRepository insightRepository,
        IPromptRepository promptRepository,
        IInsightOrchestrator orchestrator,
        ILogger<InsightsController> logger)
    {
        _insightRepository = insightRepository;
        _promptRepository = promptRepository;
        _orchestrator = orchestrator;
        _logger = logger;
    }

    [HttpPost("generate")]
    public async Task<IActionResult> GenerateInsight(
        [FromBody] GenerateInsightRequest request,
        CancellationToken cancellationToken)
    {
        try
        {
            var canGenerate = await _orchestrator.CanGenerateInsightAsync(
                request.UserId,
                request.TriggerType,
                cancellationToken);

            if (!canGenerate)
            {
                return StatusCode(429, new GenerateInsightResponse
                (
                    0,
                    "Limited",
                    "Insight limit reached for this period"
                ));
            }

            var insight = Insight.Create(request.UserId, request.TriggerType);
            await _insightRepository.AddAsync(insight, cancellationToken);

            await _orchestrator.QueueInsightGenerationAsync(insight.Id, cancellationToken);

            return Accepted(new GenerateInsightResponse
            (
                insight.Id,
                "Generating",
                "Generating your insight, please wait..."
            ));
        }

        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate insight for user {UserId}", request.UserId);
            return StatusCode(503, new GenerateInsightResponse(
                0,
                "Failed",
                "We're having trouble preparing your insight."
            ));
        }
    }

    [HttpGet("{insightId}/status")]
    public async Task<IActionResult> GetInsightStatus(int insightId, CancellationToken cancellationToken)
    {
        var insight = await _insightRepository.GetByIdAsync(insightId, cancellationToken);

        if (insight == null)
            return NotFound();

        return Ok(new GetInsightStatusResponse(
            insight.Status.Value,
            insight.Content,
            insight.GeneratedAt,
            insight.ViewedAt));
    }

    [HttpPost("{insightId}/feedback")]
    public async Task<IActionResult> SubmitFeedback(
        int insightId,
        [FromBody] SubmitFeedbackRequest request,
        CancellationToken cancellationToken)
    {
        var insight = await _insightRepository.GetByIdAsync(insightId, cancellationToken);

        if (insight == null)
            return NotFound();

        insight.AddFeedback(request.IsPositive);
        await _insightRepository.UpdateAsync(insight, cancellationToken);

        if (insight.PromptId.HasValue)
        {
            var prompt = await _promptRepository.GetByIdAsync(insight.PromptId.Value, cancellationToken);
            if (prompt != null)
            {
                if (request.IsPositive)
                    prompt.IncrementLike();
                else
                    prompt.IncrementDislike();

                await _promptRepository.UpdateAsync(prompt, cancellationToken);
            }
        }

        return Ok(new { Status = "Feedback recorded" });
    }
}

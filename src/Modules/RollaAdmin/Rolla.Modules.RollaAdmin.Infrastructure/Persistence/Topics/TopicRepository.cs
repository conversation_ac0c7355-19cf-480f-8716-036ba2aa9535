using Microsoft.EntityFrameworkCore;
using Rolla.BuildingBlocks.Application.Common;
using Rolla.Modules.RollaAdmin.Application.DTOs.Topics;
using Rolla.Modules.RollaAdmin.Application.Interfaces.Topics;
using Rolla.Modules.RollaAdmin.Core.Entities.Topics;
using Rolla.Modules.RollaAdmin.Core.Models;
using Rolla.Modules.RollaAdmin.Infrastructure.Database;

namespace Rolla.Modules.RollaAdmin.Infrastructure.Persistence.Topics;

public class TopicRepository(RollaAdminDbContext context) : ITopicRepository
{
    private readonly RollaAdminDbContext _context = context;

    public async Task<Topic?> GetByIdAsync(Guid id, CancellationToken cancellationToken)
    {
        return await _context.Topics
            .FirstOrDefaultAsync(t => t.Id == id, cancellationToken);
    }

    public async Task<Rolla.Modules.RollaAdmin.Core.Models.PagedResult<Topic>> GetAllAsync(TopicFilterRequest request, CancellationToken cancellationToken)
    {
        var query = _context.Topics.AsNoTracking();

        if (request.HasSearchTerm)
        {
            var searchTerm = request.SearchTerm!;
            query = query.Where(t =>
                EF.Functions.Like(t.Name, $"%{searchTerm}%") ||
                (t.Description != null && EF.Functions.Like(t.Description, $"%{searchTerm}%")) ||
                EF.Functions.Like(t.Prompt, $"%{searchTerm}%"));
        }

        if (request.HasActiveFilter)
        {
            query = query.Where(t => t.IsActive == request.IsActive);
        }

        if (request.HasPriorityFilter)
        {
            if (request.MinPriority.HasValue)
            {
                query = query.Where(t => t.Priority >= request.MinPriority.Value);
            }

            if (request.MaxPriority.HasValue)
            {
                query = query.Where(t => t.Priority <= request.MaxPriority.Value);
            }
        }

        if (request.CreatedAfter.HasValue)
        {
            query = query.Where(t => t.CreatedAt >= request.CreatedAfter.Value);
        }

        if (request.CreatedBefore.HasValue)
        {
            query = query.Where(t => t.CreatedAt <= request.CreatedBefore.Value);
        }

        if (request.UpdatedAfter.HasValue)
        {
            query = query.Where(t => t.UpdatedAt >= request.UpdatedAfter.Value);
        }

        if (request.UpdatedBefore.HasValue)
        {
            query = query.Where(t => t.UpdatedAt <= request.UpdatedBefore.Value);
        }

        query = ApplySorting(query, request.SortBy, request.SortDescending);

        var totalCount = await query.CountAsync(cancellationToken);

        var items = await query
            .Skip(request.Skip)
            .Take(request.Take)
            .ToListAsync(cancellationToken);

        return Rolla.Modules.RollaAdmin.Core.Models.PagedResult<Topic>.Create(items, totalCount, request.PageNumber, request.PageSize);
    }

    public async Task<List<Topic>> GetActiveTopicsAsync(CancellationToken cancellationToken)
    {
        return await _context.Topics
            .AsNoTracking()
            .Where(t => t.IsActive)
            .OrderBy(t => t.Priority)
            .ThenBy(t => t.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task<bool> ExistsByNameAsync(string name, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(name);

        return await _context.Topics
            .AsNoTracking()
            .AnyAsync(t => t.Name.ToLower() == name.ToLower(), cancellationToken);
    }

    public async Task<bool> ExistsByNameAsync(string name, Guid excludeId, CancellationToken cancellationToken)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(name);

        return await _context.Topics
            .AsNoTracking()
            .AnyAsync(t => t.Name.ToLower() == name.ToLower() && t.Id != excludeId, cancellationToken);
    }

    public async Task AddAsync(Topic topic, CancellationToken cancellationToken)
    {
        ArgumentNullException.ThrowIfNull(topic);

        _context.Topics.Add(topic);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task UpdateAsync(Topic topic, CancellationToken cancellationToken)
    {
        ArgumentNullException.ThrowIfNull(topic);

        _context.Topics.Update(topic);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task DeleteAsync(Topic topic, CancellationToken cancellationToken)
    {
        ArgumentNullException.ThrowIfNull(topic);

        _context.Topics.Remove(topic);
        await _context.SaveChangesAsync(cancellationToken);
    }

    private static IQueryable<Topic> ApplySorting(IQueryable<Topic> query, string? sortBy, bool sortDescending)
    {
        return sortBy?.ToLower() switch
        {
            "name" => sortDescending
                ? query.OrderByDescending(t => t.Name).ThenByDescending(t => t.Id)
                : query.OrderBy(t => t.Name).ThenBy(t => t.Id),

            "description" => sortDescending
                ? query.OrderByDescending(t => t.Description).ThenByDescending(t => t.Id)
                : query.OrderBy(t => t.Description).ThenBy(t => t.Id),

            "priority" => sortDescending
                ? query.OrderByDescending(t => t.Priority).ThenByDescending(t => t.Id)
                : query.OrderBy(t => t.Priority).ThenBy(t => t.Id),

            "isactive" => sortDescending
                ? query.OrderByDescending(t => t.IsActive).ThenByDescending(t => t.Id)
                : query.OrderBy(t => t.IsActive).ThenBy(t => t.Id),

            "updatedat" => sortDescending
                ? query.OrderByDescending(t => t.UpdatedAt).ThenByDescending(t => t.Id)
                : query.OrderBy(t => t.UpdatedAt).ThenBy(t => t.Id),

            "createdat" or _ => sortDescending
                ? query.OrderByDescending(t => t.CreatedAt).ThenByDescending(t => t.Id)
                : query.OrderBy(t => t.CreatedAt).ThenBy(t => t.Id)
        };
    }
}

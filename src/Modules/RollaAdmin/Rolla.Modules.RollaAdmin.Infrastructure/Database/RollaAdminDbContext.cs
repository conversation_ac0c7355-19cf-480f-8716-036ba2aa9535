using System.Text.Json;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Rolla.Modules.RollaAdmin.Infrastructure.Database.Converters;
using Rolla.BuildingBlocks.Infrastructure.Database;
using Rolla.Modules.RollaAdmin.Core.Entities.Guidelines;
using Rolla.Modules.RollaAdmin.Core.Entities.Prompts;
using Rolla.Modules.RollaAdmin.Core.Entities.PromptTriggers;
using Rolla.Modules.RollaAdmin.Core.Entities.Topics;

namespace Rolla.Modules.RollaAdmin.Infrastructure.Database;

public class RollaAdminDbContext : BaseDbContext
{
    public DbSet<Prompt> Prompts { get; set; }
    public DbSet<Guideline> Guidelines { get; set; }
    public DbSet<Topic> Topics { get; set; }
    public DbSet<PromptTrigger> PromptTriggers { get; set; }

    public RollaAdminDbContext(DbContextOptions<RollaAdminDbContext> options) : base(options)
    {
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        modelBuilder.Entity<Prompt>(entity =>
            {
                entity.ToTable("system_prompts");

                entity.HasKey(e => e.Id);

                entity.Property(e => e.Name)
                    .HasColumnName("name")
                    .HasMaxLength(100)
                    .IsRequired();

                entity.Property(e => e.Description)
                    .HasColumnName("description")
                    .HasMaxLength(500);

                entity.Property(e => e.SystemPromptContent)
                    .HasColumnName("system_prompt_content")
                    .HasMaxLength(5000)
                    .IsRequired();

                entity.Property(e => e.IsActive)
                    .HasColumnName("is_active")
                    .IsRequired();

                entity.Property(e => e.CreatedAt)
                    .HasColumnName("created_at")
                    .IsRequired();

                entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");

                entity.HasIndex(e => e.Name).IsUnique();
                entity.HasIndex(e => e.IsActive);
            });

        modelBuilder.Entity<Guideline>(entity =>
            {
                entity.ToTable("prompt_guidelines");

                entity.HasKey(e => e.Id);

                entity.Property(e => e.Name)
                    .HasColumnName("name")
                    .HasMaxLength(100)
                    .IsRequired();

                entity.Property(e => e.Description)
                    .HasColumnName("description")
                    .HasMaxLength(500);

                entity.Property(e => e.PromptGuidelineContent)
                    .HasColumnName("prompt_guideline_content")
                    .HasMaxLength(5000)
                    .IsRequired();

                entity.Property(e => e.IsActive)
                    .HasColumnName("is_active")
                    .IsRequired();

                entity.Property(e => e.CreatedAt)
                    .HasColumnName("created_at")
                    .IsRequired();

                entity.Property(e => e.UpdatedAt)
                    .HasColumnName("updated_at");

                entity.HasIndex(e => e.Name)
                    .IsUnique();

                entity.HasIndex(e => e.IsActive);

            });

        modelBuilder.Entity<Topic>(entity =>
            {
                entity.ToTable("prompt_topics");

                entity.HasKey(e => e.Id);

                entity.Property(e => e.Name)
                    .HasColumnName("name")
                    .HasMaxLength(100)
                    .IsRequired();

                entity.Property(e => e.Description)
                    .HasColumnName("description")
                    .HasMaxLength(500);

                entity.Property(e => e.Prompt)
                    .HasColumnName("prompt")
                    .HasMaxLength(5000)
                    .IsRequired();

                entity.Property(e => e.Priority)
                    .HasColumnName("priority")
                    .IsRequired();

                entity.Property(e => e.IsActive)
                    .HasColumnName("is_active")
                    .IsRequired();

                entity.Property(e => e.CreatedAt)
                    .HasColumnName("created_at")
                    .IsRequired();

                entity.Property(e => e.UpdatedAt)
                    .HasColumnName("updated_at");

                entity.HasIndex(e => e.Name).IsUnique();
                entity.HasIndex(e => e.IsActive);
                entity.HasIndex(e => e.Priority);
            });

        modelBuilder.Entity<PromptTrigger>(entity =>
        {
            entity.ToTable("prompt_triggers");

            entity.HasKey(pt => pt.Id);

            entity.Property(pt => pt.Id)
                .HasColumnName("id")
                .IsRequired();

            entity.Property(pt => pt.Name)
                .HasColumnName("name")
                .HasMaxLength(100)
                .IsRequired();

            entity.Property(pt => pt.Description)
                .HasColumnName("description")
                .HasMaxLength(500);

            entity.Property(pt => pt.Priority)
                .HasColumnName("priority")
                .IsRequired();

            entity.Property(pt => pt.InsightNumber)
                .HasColumnName("insight_number")
                .IsRequired();

            entity.Property(pt => pt.CooldownMinutes)
                .HasColumnName("cooldown_minutes")
                .IsRequired();

            entity.Property(pt => pt.DailyLimit)
                .HasColumnName("daily_limit")
                .IsRequired();

            entity.Property(pt => pt.IsActive)
                .HasColumnName("is_active")
                .IsRequired();

            entity.Property(pt => pt.TriggerCondition)
                .HasColumnName("trigger_condition")
                .HasConversion<int>()
                .IsRequired();

            entity.Property(pt => pt.ConditionType)
                .HasColumnName("condition_type")
                .HasConversion<int>()
                .IsRequired();

            entity.Property(pt => pt.ScoreType)
                .HasColumnName("score_type")
                .HasConversion<int?>();

            entity.Property(pt => pt.ScoreOperator)
                .HasColumnName("score_operator")
                .HasConversion<int?>();

            entity.Property(pt => pt.ScoreValue)
                .HasColumnName("score_value")
                .HasMaxLength(50);

            entity.Property(pt => pt.StartTime)
                .HasColumnName("start_time");

            entity.Property(pt => pt.EndTime)
                .HasColumnName("end_time");

            entity.Property(pt => pt.MetricType)
                .HasColumnName("metric_type")
                .HasConversion<int?>();

            entity.Property(pt => pt.MetricOperator)
                .HasColumnName("metric_operator")
                .HasConversion<int?>();

            entity.Property(pt => pt.MetricValue)
                .HasColumnName("metric_value")
                .HasMaxLength(50);

            entity.Property<string?>("_associatedTopicsJson")
                .HasColumnName("associated_topics")
                .HasColumnType("varchar(500)")
                .IsRequired(false);

            entity.Ignore(pt => pt.AssociatedTopicIds);

            entity.Property(pt => pt.CreatedAt)
                .HasColumnName("created_at")
                .IsRequired();

            entity.Property(pt => pt.UpdatedAt)
                .HasColumnName("updated_at");

            entity.HasIndex(pt => pt.Name)
                .IsUnique()
                .HasDatabaseName("ix_prompt_triggers_name");

            entity.HasIndex(pt => pt.IsActive)
                .HasDatabaseName("ix_prompt_triggers_is_active");

            entity.HasIndex(pt => pt.Priority)
                .HasDatabaseName("ix_prompt_triggers_priority");

            entity.HasIndex(pt => pt.InsightNumber)
                .HasDatabaseName("ix_prompt_triggers_insight_number");

            entity.HasIndex(pt => pt.ConditionType)
                .HasDatabaseName("ix_prompt_triggers_condition_type");

            entity.HasIndex(pt => pt.CreatedAt)
                .HasDatabaseName("ix_prompt_triggers_created_at");
        });

    }
}

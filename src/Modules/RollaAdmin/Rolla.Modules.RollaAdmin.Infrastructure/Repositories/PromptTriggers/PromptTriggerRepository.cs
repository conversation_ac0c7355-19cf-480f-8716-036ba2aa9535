using Microsoft.EntityFrameworkCore;
using Rolla.Modules.RollaAdmin.Application.DTOs.PromptTriggers;
using Rolla.Modules.RollaAdmin.Application.Interfaces.PromptTriggers;
using Rolla.Modules.RollaAdmin.Core.Entities.PromptTriggers;
using Rolla.Modules.RollaAdmin.Core.Entities.PromptTriggers.Enums;
using Rolla.Modules.RollaAdmin.Core.Models;
using Rolla.Modules.RollaAdmin.Infrastructure.Database;

namespace Rolla.Modules.RollaAdmin.Infrastructure.Repositories.PromptTriggers;

public class PromptTriggerRepository : IPromptTriggerRepository
{
    private readonly RollaAdminDbContext _context;

    public PromptTriggerRepository(RollaAdminDbContext context)
    {
        _context = context;
    }

    public async Task<PagedResult<PromptTrigger>> GetAllAsync(PromptTriggerFilterRequest request, CancellationToken cancellationToken)
    {
        var query = _context.PromptTriggers.AsQueryable();

        // Apply filters
        if (request.HasSearchTerm)
        {
            query = query.Where(pt => pt.Name.Contains(request.SearchTerm!) ||
                                     (pt.Description != null && pt.Description.Contains(request.SearchTerm!)));
        }

        if (request.HasActiveFilter)
        {
            query = query.Where(pt => pt.IsActive == request.IsActive);
        }

        if (request.HasPriorityFilter)
        {
            if (request.MinPriority.HasValue)
                query = query.Where(pt => pt.Priority >= request.MinPriority.Value);

            if (request.MaxPriority.HasValue)
                query = query.Where(pt => pt.Priority <= request.MaxPriority.Value);
        }

        if (request.HasInsightNumberFilter)
        {
            query = query.Where(pt => pt.InsightNumber == request.InsightNumber);
        }

        if (request.HasTriggerConditionFilter)
        {
            query = query.Where(pt => pt.TriggerCondition == request.TriggerCondition);
        }

        if (request.HasConditionTypeFilter)
        {
            query = query.Where(pt => pt.ConditionType == request.ConditionType);
        }

        if (request.CreatedAfter.HasValue)
        {
            query = query.Where(pt => pt.CreatedAt >= request.CreatedAfter.Value);
        }

        if (request.CreatedBefore.HasValue)
        {
            query = query.Where(pt => pt.CreatedAt <= request.CreatedBefore.Value);
        }

        if (request.UpdatedAfter.HasValue)
        {
            query = query.Where(pt => pt.UpdatedAt >= request.UpdatedAfter.Value);
        }

        if (request.UpdatedBefore.HasValue)
        {
            query = query.Where(pt => pt.UpdatedAt <= request.UpdatedBefore.Value);
        }

        // Apply sorting
        query = ApplySorting(query, request.SortBy, request.SortDescending);

        // Get total count
        var totalCount = await query.CountAsync(cancellationToken);

        // Apply pagination
        var items = await query
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .ToListAsync(cancellationToken);

        return PagedResult<PromptTrigger>.Create(items, totalCount, request.PageNumber, request.PageSize);
    }

    public async Task<PromptTrigger?> GetByIdAsync(Guid id, CancellationToken cancellationToken)
    {
        return await _context.PromptTriggers
            .FirstOrDefaultAsync(pt => pt.Id == id, cancellationToken);
    }

    public async Task<bool> ExistsByNameAsync(string name, CancellationToken cancellationToken = default)
    {
        return await _context.PromptTriggers
            .AnyAsync(pt => pt.Name == name, cancellationToken);
    }

    public async Task<bool> ExistsByNameAsync(string name, Guid excludeId, CancellationToken cancellationToken)
    {
        return await _context.PromptTriggers
            .AnyAsync(pt => pt.Name == name && pt.Id != excludeId, cancellationToken);
    }

    public async Task AddAsync(PromptTrigger promptTrigger, CancellationToken cancellationToken)
    {
        await _context.PromptTriggers.AddAsync(promptTrigger, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task UpdateAsync(PromptTrigger promptTrigger, CancellationToken cancellationToken)
    {
        _context.PromptTriggers.Update(promptTrigger);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task DeleteAsync(PromptTrigger promptTrigger, CancellationToken cancellationToken)
    {
        _context.PromptTriggers.Remove(promptTrigger);
        await _context.SaveChangesAsync(cancellationToken);
    }

    private static IQueryable<PromptTrigger> ApplySorting(IQueryable<PromptTrigger> query, string? sortBy, bool sortDescending)
    {
        if (string.IsNullOrEmpty(sortBy))
            sortBy = "CreatedAt";

        query = sortBy.ToLower() switch
        {
            "name" => sortDescending ? query.OrderByDescending(pt => pt.Name) : query.OrderBy(pt => pt.Name),
            "description" => sortDescending ? query.OrderByDescending(pt => pt.Description) : query.OrderBy(pt => pt.Description),
            "priority" => sortDescending ? query.OrderByDescending(pt => pt.Priority) : query.OrderBy(pt => pt.Priority),
            "insightnumber" => sortDescending ? query.OrderByDescending(pt => pt.InsightNumber) : query.OrderBy(pt => pt.InsightNumber),
            "cooldownminutes" => sortDescending ? query.OrderByDescending(pt => pt.CooldownMinutes) : query.OrderBy(pt => pt.CooldownMinutes),
            "dailylimit" => sortDescending ? query.OrderByDescending(pt => pt.DailyLimit) : query.OrderBy(pt => pt.DailyLimit),
            "isactive" => sortDescending ? query.OrderByDescending(pt => pt.IsActive) : query.OrderBy(pt => pt.IsActive),
            "triggercondition" => sortDescending ? query.OrderByDescending(pt => pt.TriggerCondition) : query.OrderBy(pt => pt.TriggerCondition),
            "conditiontype" => sortDescending ? query.OrderByDescending(pt => pt.ConditionType) : query.OrderBy(pt => pt.ConditionType),
            "updatedat" => sortDescending ? query.OrderByDescending(pt => pt.UpdatedAt) : query.OrderBy(pt => pt.UpdatedAt),
            _ => sortDescending ? query.OrderByDescending(pt => pt.CreatedAt) : query.OrderBy(pt => pt.CreatedAt)
        };

        return query;
    }
}

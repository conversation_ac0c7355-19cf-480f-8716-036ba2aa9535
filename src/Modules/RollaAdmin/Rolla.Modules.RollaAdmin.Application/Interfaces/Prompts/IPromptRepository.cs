using Rolla.Modules.RollaAdmin.Core.Entities.Prompts;
using Rolla.Modules.RollaAdmin.Core.Models;
using Rolla.Modules.RollaAdmin.Application.DTOs.Prompts;

namespace Rolla.Modules.RollaAdmin.Application.Interfaces.Prompts;

public interface IPromptRepository
{
    Task<Prompt?> GetByIdAsync(Guid id, CancellationToken cancellationToken);
    Task<PagedResult<Prompt>> GetAllAsync(PromptFilterRequest request, CancellationToken cancellationToken);
    Task<Prompt?> GetActivePromptAsync(CancellationToken cancellationToken);
    Task<bool> ExistsByNameAsync(string name, CancellationToken cancellationToken);
    Task<bool> ExistsByNameAsync(string name, Guid excludeId, CancellationToken cancellationToken);
    Task AddAsync(Prompt prompt, CancellationToken cancellationToken);
    Task UpdateAsync(Prompt prompt, CancellationToken cancellationToken);
    Task DeleteAsync(Prompt prompt, CancellationToken cancellationToken);
}

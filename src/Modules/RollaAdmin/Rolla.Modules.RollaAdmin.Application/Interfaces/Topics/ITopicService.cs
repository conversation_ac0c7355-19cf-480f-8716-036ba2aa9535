using Rolla.BuildingBlocks.Application.Validation;
using Rolla.Modules.RollaAdmin.Application.DTOs.Topics;
using Rolla.Modules.RollaAdmin.Core.Models;

namespace Rolla.Modules.RollaAdmin.Application.Interfaces.Topics;

public interface ITopicService
{
    Task<Result<Rolla.Modules.RollaAdmin.Core.Models.PagedResult<TopicDto>>> GetAllTopicsAsync(TopicFilterRequest request, CancellationToken cancellationToken);
    Task<Result<TopicDto>> GetTopicByIdAsync(Guid id, CancellationToken cancellationToken);
    Task<Result<List<TopicDto>>> GetActiveTopicsAsync(CancellationToken cancellationToken);
    Task<Result<TopicDto>> CreateTopicAsync(CreateTopicDto dto, CancellationToken cancellationToken);
    Task<Result<TopicDto>> UpdateTopicAsync(Guid id, UpdateTopicDto dto, CancellationToken cancellationToken);
    Task<Result<bool>> ActivateTopicAsync(Guid id, CancellationToken cancellationToken);
    Task<Result<bool>> DeactivateTopicAsync(Guid id, CancellationToken cancellationToken);
    Task<Result<bool>> DeleteTopicAsync(Guid id, CancellationToken cancellationToken);
}

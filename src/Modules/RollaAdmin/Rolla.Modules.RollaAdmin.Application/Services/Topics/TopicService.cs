using FluentValidation;
using Microsoft.Extensions.Logging;
using Rolla.BuildingBlocks.Application.Common;
using Rolla.BuildingBlocks.Application.Validation;
using Rolla.Modules.RollaAdmin.Application.DTOs.Topics;
using Rolla.Modules.RollaAdmin.Application.Extensions;
using Rolla.Modules.RollaAdmin.Application.Interfaces.Topics;
using Rolla.Modules.RollaAdmin.Core.Entities.Topics;
using Rolla.Modules.RollaAdmin.Core.Models;

namespace Rolla.Modules.RollaAdmin.Application.Services.Topics;

public class TopicService(
    ITopicRepository topicRepository,
    IValidator<CreateTopicDto> createValidator,
    IValidator<UpdateTopicDto> updateValidator,
    IValidator<TopicFilterRequest> filterValidator,
    ILogger<TopicService> logger) : ITopicService
{
    private readonly ITopicRepository _topicRepository = topicRepository;
    private readonly IValidator<CreateTopicDto> _createValidator = createValidator;
    private readonly IValidator<UpdateTopicDto> _updateValidator = updateValidator;
    private readonly IValidator<TopicFilterRequest> _filterValidator = filterValidator;
    private readonly ILogger<TopicService> _logger = logger;

    public async Task<Result<Rolla.Modules.RollaAdmin.Core.Models.PagedResult<TopicDto>>> GetAllTopicsAsync(TopicFilterRequest request, CancellationToken cancellationToken)
    {
        try
        {
            var validationResult = await _filterValidator.ValidateAsync(request, cancellationToken);
            if (!validationResult.IsValid)
            {
                var errors = validationResult.Errors.Select(e => e.ErrorMessage).ToList();
                _logger.LogWarning("Invalid filter request for topics: {Errors}", string.Join(", ", errors));
                return Result<Rolla.Modules.RollaAdmin.Core.Models.PagedResult<TopicDto>>.Failure(errors);
            }

            var pagedTopics = await _topicRepository.GetAllAsync(request, cancellationToken);
            var topicDtos = pagedTopics.Items.Select(t => t.ToDto()).ToList();

            var result = Rolla.Modules.RollaAdmin.Core.Models.PagedResult<TopicDto>.Create(topicDtos, pagedTopics.TotalCount, pagedTopics.PageNumber, pagedTopics.PageSize);

            _logger.LogInformation("Retrieved {Count} topics (page {PageNumber} of {TotalPages})",
                result.Items.Count(), result.PageNumber, result.TotalPages);

            return Result<Rolla.Modules.RollaAdmin.Core.Models.PagedResult<TopicDto>>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while retrieving topics");
            return Result<Rolla.Modules.RollaAdmin.Core.Models.PagedResult<TopicDto>>.Failure($"Failed to retrieve topics: {ex.Message}");
        }
    }

    public async Task<Result<TopicDto>> GetTopicByIdAsync(Guid id, CancellationToken cancellationToken)
    {
        try
        {
            var topic = await _topicRepository.GetByIdAsync(id, cancellationToken);
            if (topic == null)
            {
                _logger.LogWarning("Topic with ID {TopicId} not found", id);
                return Result<TopicDto>.Failure("Topic not found");
            }

            _logger.LogInformation("Retrieved topic {TopicId}: {TopicName}", topic.Id, topic.Name);
            return Result<TopicDto>.Success(topic.ToDto());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while retrieving topic {TopicId}", id);
            return Result<TopicDto>.Failure($"Failed to retrieve topic: {ex.Message}");
        }
    }

    public async Task<Result<List<TopicDto>>> GetActiveTopicsAsync(CancellationToken cancellationToken)
    {
        try
        {
            var activeTopics = await _topicRepository.GetActiveTopicsAsync(cancellationToken);
            var topicDtos = activeTopics.Select(t => t.ToDto()).ToList();

            _logger.LogInformation("Retrieved {Count} active topics", topicDtos.Count);
            return Result<List<TopicDto>>.Success(topicDtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while retrieving active topics");
            return Result<List<TopicDto>>.Failure($"Failed to retrieve active topics: {ex.Message}");
        }
    }

    public async Task<Result<TopicDto>> CreateTopicAsync(CreateTopicDto dto, CancellationToken cancellationToken)
    {
        try
        {
            var validationResult = await _createValidator.ValidateAsync(dto, cancellationToken);
            if (!validationResult.IsValid)
            {
                var errors = validationResult.Errors.Select(e => e.ErrorMessage).ToList();
                _logger.LogWarning("Invalid create request for topic {TopicName}: {Errors}",
                    dto.Name, string.Join(", ", errors));
                return Result<TopicDto>.Failure(errors);
            }

            var nameExists = await _topicRepository.ExistsByNameAsync(dto.Name, cancellationToken);
            if (nameExists)
            {
                _logger.LogWarning("Topic with name {TopicName} already exists", dto.Name);
                return Result<TopicDto>.Failure("A topic with this name already exists");
            }

            var topic = new Topic(dto.Name, dto.Description, dto.Prompt, dto.Priority);
            await _topicRepository.AddAsync(topic, cancellationToken);

            _logger.LogInformation("Created topic {TopicId}: {TopicName}", topic.Id, topic.Name);
            return Result<TopicDto>.Success(topic.ToDto());
        }
        catch (Exception ex) when (ex is not InvalidOperationException)
        {
            _logger.LogError(ex, "Error occurred while creating topic: {TopicName}", dto.Name);
            return Result<TopicDto>.Failure($"Failed to create topic: {ex.Message}");
        }
    }

    public async Task<Result<TopicDto>> UpdateTopicAsync(Guid id, UpdateTopicDto dto, CancellationToken cancellationToken)
    {
        try
        {
            var validationResult = await _updateValidator.ValidateAsync(dto, cancellationToken);
            if (!validationResult.IsValid)
            {
                var errors = validationResult.Errors.Select(e => e.ErrorMessage).ToList();
                _logger.LogWarning("Invalid update request for topic {TopicId}: {Errors}",
                    id, string.Join(", ", errors));
                return Result<TopicDto>.Failure(errors);
            }

            var topic = await _topicRepository.GetByIdAsync(id, cancellationToken);
            if (topic == null)
            {
                _logger.LogWarning("Topic with ID {TopicId} not found for update", id);
                return Result<TopicDto>.Failure("Topic not found");
            }

            if (!string.IsNullOrWhiteSpace(dto.Name) && dto.Name != topic.Name)
            {
                var nameExists = await _topicRepository.ExistsByNameAsync(dto.Name, id, cancellationToken);
                if (nameExists)
                {
                    _logger.LogWarning("Topic with name {TopicName} already exists", dto.Name);
                    return Result<TopicDto>.Failure("A topic with this name already exists");
                }
            }

            topic.Update(dto.Name, dto.Description, dto.Prompt, dto.Priority);
            await _topicRepository.UpdateAsync(topic, cancellationToken);

            _logger.LogInformation("Updated topic {TopicId}: {TopicName}", topic.Id, topic.Name);
            return Result<TopicDto>.Success(topic.ToDto());
        }
        catch (Exception ex) when (ex is not InvalidOperationException)
        {
            _logger.LogError(ex, "Error occurred while updating topic {TopicId}", id);
            return Result<TopicDto>.Failure($"Failed to update topic: {ex.Message}");
        }
    }

    public async Task<Result<bool>> ActivateTopicAsync(Guid id, CancellationToken cancellationToken)
    {
        try
        {
            var topic = await _topicRepository.GetByIdAsync(id, cancellationToken);
            if (topic == null)
            {
                _logger.LogWarning("Topic with ID {TopicId} not found for activation", id);
                return Result<bool>.Failure("Topic not found");
            }

            if (topic.IsActive)
            {
                _logger.LogInformation("Topic {TopicId} is already active", id);
                return Result<bool>.Success(true);
            }

            topic.Activate();
            await _topicRepository.UpdateAsync(topic, cancellationToken);

            _logger.LogInformation("Activated topic {TopicId}: {TopicName}", topic.Id, topic.Name);
            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while activating topic {TopicId}", id);
            return Result<bool>.Failure($"Failed to activate topic: {ex.Message}");
        }
    }

    public async Task<Result<bool>> DeactivateTopicAsync(Guid id, CancellationToken cancellationToken)
    {
        try
        {
            var topic = await _topicRepository.GetByIdAsync(id, cancellationToken);
            if (topic == null)
            {
                _logger.LogWarning("Topic with ID {TopicId} not found for deactivation", id);
                return Result<bool>.Failure("Topic not found");
            }

            if (!topic.IsActive)
            {
                _logger.LogInformation("Topic {TopicId} is already inactive", id);
                return Result<bool>.Success(true);
            }

            topic.Deactivate();
            await _topicRepository.UpdateAsync(topic, cancellationToken);

            _logger.LogInformation("Deactivated topic {TopicId}: {TopicName}", topic.Id, topic.Name);
            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while deactivating topic {TopicId}", id);
            return Result<bool>.Failure($"Failed to deactivate topic: {ex.Message}");
        }
    }

    public async Task<Result<bool>> DeleteTopicAsync(Guid id, CancellationToken cancellationToken)
    {
        try
        {
            var topic = await _topicRepository.GetByIdAsync(id, cancellationToken);
            if (topic == null)
            {
                _logger.LogWarning("Topic with ID {TopicId} not found for deletion", id);
                return Result<bool>.Failure("Topic not found");
            }

            await _topicRepository.DeleteAsync(topic, cancellationToken);

            _logger.LogInformation("Deleted topic {TopicId}: {TopicName}", topic.Id, topic.Name);
            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while deleting topic {TopicId}", id);
            return Result<bool>.Failure($"Failed to delete topic: {ex.Message}");
        }
    }
}

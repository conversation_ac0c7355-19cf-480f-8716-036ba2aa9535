using FluentValidation;
using Microsoft.Extensions.Logging;
using Rolla.BuildingBlocks.Application.Common;
using Rolla.BuildingBlocks.Application.Validation;
using Rolla.Modules.RollaAdmin.Application.DTOs.Guidelines;
using Rolla.Modules.RollaAdmin.Application.Extensions;
using Rolla.Modules.RollaAdmin.Application.Interfaces.Guidelines;
using Rolla.Modules.RollaAdmin.Core.Entities.Guidelines;

namespace Rolla.Modules.RollaAdmin.Application.Services.Guidelines;

public class GuidelineService(
    IGuidelineRepository repository,
    IValidator<GuidelineFilterRequest> filterValidator,
    IValidator<CreateGuidelineDto> createValidator,
    IValidator<UpdateGuidelineDto> updateValidator,
    ILogger<GuidelineService> logger) : IGuidelineService
{
    private readonly IGuidelineRepository _repository = repository;
    private readonly IValidator<GuidelineFilterRequest> _filterValidator = filterValidator;
    private readonly IValidator<CreateGuidelineDto> _createValidator = createValidator;
    private readonly IValidator<UpdateGuidelineDto> _updateValidator = updateValidator;
    private readonly ILogger<GuidelineService> _logger = logger;

    public async Task<Result<PagedResult<GuidelineDto>>> GetAllGuidelinesAsync(GuidelineFilterRequest request, CancellationToken cancellationToken)
    {
        try
        {
            var validationResult = await _filterValidator.ValidateAsync(request, cancellationToken);
            if (!validationResult.IsValid)
            {
                var errors = validationResult.Errors.Select(e => e.ErrorMessage).ToList();
                _logger.LogWarning("Invalid filter request: {Errors}", string.Join(", ", errors));
                throw new ValidationException(validationResult.Errors);
            }

            var pagedGuidelines = await _repository.GetAllAsync(request, cancellationToken);

            var guidelineDtos = pagedGuidelines.Items.Select(p => p.ToDto());

            return Result<PagedResult<GuidelineDto>>.Success(PagedResult<GuidelineDto>.Create(
                guidelineDtos,
                pagedGuidelines.TotalCount,
                pagedGuidelines.PageNumber,
                pagedGuidelines.PageSize));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve guidelines with filters: {SearchTerm}, {IsActive}",
                request.SearchTerm, request.IsActive);
            return Result<PagedResult<GuidelineDto>>.Failure($"Failed to retrieve guidelines: {ex.Message}");

        }
    }
    public async Task<Result<GuidelineDto>> GetGuidelineByIdAsync(Guid id, CancellationToken cancellationToken)
    {
        try
        {
            var guideline = await _repository.GetByIdAsync(id, cancellationToken);
            if (guideline == null)
            {
                _logger.LogWarning("Error occurred while retrieving guideline with ID: {GuidelineId}", id);
                return Result<GuidelineDto>.Failure("Guideline not found");

            }

            return Result<GuidelineDto>.Success(guideline.ToDto());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while retrieving guideline with ID: {GuidelineId}", id);
            return Result<GuidelineDto>.Failure($"Failed to retrieve guideline: {ex.Message}");
        }
    }
    public async Task<Result<GuidelineDto>> GetActiveGuidelineAsync(CancellationToken cancellationToken)
    {
        try
        {
            var guideline = await _repository.GetActiveGuidelineAsync(cancellationToken);
            if (guideline == null)
            {
                _logger.LogWarning("Error occurred while retrieving active guideline");
                return Result<GuidelineDto>.Failure("No active guideline found");
            }

            return Result<GuidelineDto>.Success(guideline.ToDto());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while retrieving active guideline");
            return Result<GuidelineDto>.Failure($"Failed to retrieve active guideline: {ex.Message}");
        }
    }
    public async Task<Result<GuidelineDto>> CreateGuidelineAsync(CreateGuidelineDto dto, CancellationToken cancellationToken)
    {
        try
        {
            var validationResult = await _createValidator.ValidateAsync(dto, cancellationToken);
            if (!validationResult.IsValid)
            {
                var errors = validationResult.Errors.Select(e => e.ErrorMessage).ToList();
                _logger.LogWarning("Invalid create request for guideline {GuidelineName}: {Errors}",
                    dto.Name, string.Join(", ", errors));
                return Result<GuidelineDto>.Failure(errors);
            }

            var nameExists = await _repository.ExistsByNameAsync(dto.Name, cancellationToken: cancellationToken);
            if (nameExists)
            {
                _logger.LogWarning("Guideline with name {GuidelineName} already exists", dto.Name);
                return Result<GuidelineDto>.Failure("A guideline with this name already exists");
            }

            var guideline = new Guideline(
                Guid.NewGuid(),
                dto.Name,
                dto.Description,
                dto.PromptGuidelineContent
            );

            await _repository.AddAsync(guideline, cancellationToken);

            return Result<GuidelineDto>.Success(guideline.ToDto());
        }
        catch (Exception ex) when (ex is not InvalidOperationException)
        {
            _logger.LogError(ex, "Error occurred while creating guideline: {GuidelineName}", dto.Name);
            return Result<GuidelineDto>.Failure($"Failed to create guideline: {ex.Message}");
        }
    }
    public async Task<Result<GuidelineDto>> UpdateGuidelineAsync(Guid id, UpdateGuidelineDto dto, CancellationToken cancellationToken)
    {
        try
        {
            var validationResult = await _updateValidator.ValidateAsync(dto, cancellationToken);
            if (!validationResult.IsValid)
            {
                var errors = validationResult.Errors.Select(e => e.ErrorMessage).ToList();
                _logger.LogWarning("Invalid update request for guideline {GuidelineId}: {Errors}",
                    id, string.Join(", ", errors));
                return Result<GuidelineDto>.Failure(errors);
            }

            var guideline = await _repository.GetByIdAsync(id, cancellationToken);
            if (guideline == null)
            {
                _logger.LogWarning("Guideline not found for update with ID: {GuidelineId}", id);
                return Result<GuidelineDto>.Failure("Prompt not found");
            }

            if (!string.IsNullOrWhiteSpace(dto.Name) && dto.Name != guideline.Name)
            {
                var nameExists = await _repository.ExistsByNameAsync(dto.Name, id, cancellationToken);
                if (nameExists)
                {
                    _logger.LogWarning("Guideline with name {GuidelineName} already exists", dto.Name);
                    return Result<GuidelineDto>.Failure("A guideline with this name already exists");
                }
            }

            guideline.Update(
                dto.Name,
                dto.Description,
                dto.PromptGuidelineContent
            );
            await _repository.UpdateAsync(guideline, cancellationToken);

            return Result<GuidelineDto>.Success(guideline.ToDto());
        }
        catch (Exception ex) when (ex is not InvalidOperationException)
        {
            _logger.LogError(ex, "Error occurred while updating guideline with ID: {GuidelineId}", id);
            return Result<GuidelineDto>.Failure($"Failed to update guideline: {ex.Message}");
        }
    }
    public async Task<Result<bool>> ActivateGuidelineAsync(Guid id, CancellationToken cancellationToken)
    {
        try
        {
            var guideline = await _repository.GetByIdAsync(id, cancellationToken);
            if (guideline == null)
            {
                _logger.LogWarning("Guideline not found for activation with ID: {GuidelineId}", id);
                return Result<bool>.Failure("Guideline not found");
            }

            var existingGuideline = await _repository.GetActiveGuidelineAsync(cancellationToken);

            if (existingGuideline != null && existingGuideline.Id != id)
            {
                existingGuideline.Deactivate();
                await _repository.UpdateAsync(existingGuideline, cancellationToken);
            }

            guideline.Activate();
            await _repository.UpdateAsync(guideline, cancellationToken);

            return Result<bool>.Success(true);
        }
        catch (Exception ex) when (ex is not InvalidOperationException)
        {
            _logger.LogError(ex, "Error occurred while activating guideline with ID: {GuidelineId}", id);
            return Result<bool>.Failure($"Failed to activate guideline: {ex.Message}");
        }
    }
    public async Task<Result<bool>> DeleteGuidelineAsync(Guid id, CancellationToken cancellationToken)
    {
        try
        {
            var guideline = await _repository.GetByIdAsync(id, cancellationToken);
            if (guideline == null)
            {
                _logger.LogWarning("Guideline not found for deletion with ID: {GuidelineId}", id);
                return Result<bool>.Failure("Guideline not found");
            }

            await _repository.DeleteAsync(guideline, cancellationToken);
            return Result<bool>.Success(true);
        }
        catch (Exception ex) when (ex is not InvalidOperationException)
        {
            _logger.LogError(ex, "Error occurred while deleting guideline with ID: {GuidelineId}", id);
            return Result<bool>.Failure($"Failed to delete guideline: {ex.Message}");
        }
    }
}

using FluentValidation;
using Microsoft.Extensions.Logging;
using Rolla.BuildingBlocks.Application.Validation;
using Rolla.Modules.RollaAdmin.Application.DTOs.PromptTriggers;
using Rolla.Modules.RollaAdmin.Application.Extensions;
using Rolla.Modules.RollaAdmin.Application.Interfaces.PromptTriggers;
using Rolla.Modules.RollaAdmin.Core.Entities.PromptTriggers;
using Rolla.Modules.RollaAdmin.Core.Entities.PromptTriggers.Enums;
using Rolla.Modules.RollaAdmin.Core.Models;

namespace Rolla.Modules.RollaAdmin.Application.Services.PromptTriggers;

public class PromptTriggerService : IPromptTriggerService
{
    private readonly IPromptTriggerRepository _repository;
    private readonly IValidator<CreatePromptTriggerDto> _createValidator;
    private readonly IValidator<UpdatePromptTriggerDto> _updateValidator;
    private readonly IValidator<PromptTriggerFilterRequest> _filterValidator;
    private readonly ILogger<PromptTriggerService> _logger;

    public PromptTriggerService(
        IPromptTriggerRepository repository,
        IValidator<CreatePromptTriggerDto> createValidator,
        IValidator<UpdatePromptTriggerDto> updateValidator,
        IValidator<PromptTriggerFilterRequest> filterValidator,
        ILogger<PromptTriggerService> logger)
    {
        _repository = repository;
        _createValidator = createValidator;
        _updateValidator = updateValidator;
        _filterValidator = filterValidator;
        _logger = logger;
    }

    public async Task<Result<PagedResult<PromptTriggerDto>>> GetAllPromptTriggersAsync(
        PromptTriggerFilterRequest request,
        CancellationToken cancellationToken)
    {
        try
        {
            var validationResult = await _filterValidator.ValidateAsync(request, cancellationToken);
            if (!validationResult.IsValid)
            {
                return Result<PagedResult<PromptTriggerDto>>.Failure(validationResult.Errors.Select(e => e.ErrorMessage).ToList());
            }

            var pagedResult = await _repository.GetAllAsync(request, cancellationToken);
            var dtos = pagedResult.Items.Select(pt => pt.ToDto()).ToList();

            var result = PagedResult<PromptTriggerDto>.Create(
                dtos,
                pagedResult.TotalCount,
                pagedResult.PageNumber,
                pagedResult.PageSize);

            return Result<PagedResult<PromptTriggerDto>>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while retrieving prompt triggers");
            return Result<PagedResult<PromptTriggerDto>>.Failure(new List<string> { "An error occurred while retrieving prompt triggers from the database" });
        }
    }

    public async Task<Result<PromptTriggerDto>> GetPromptTriggerByIdAsync(Guid id, CancellationToken cancellationToken)
    {
        try
        {
            var promptTrigger = await _repository.GetByIdAsync(id, cancellationToken);
            if (promptTrigger == null)
            {
                return Result<PromptTriggerDto>.Failure(new List<string> { "Prompt trigger not found" });
            }

            return Result<PromptTriggerDto>.Success(promptTrigger.ToDto());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while retrieving prompt trigger with ID {Id}", id);
            return Result<PromptTriggerDto>.Failure(new List<string> { "An error occurred while retrieving the prompt trigger" });
        }
    }

    public async Task<Result<PromptTriggerDto>> CreatePromptTriggerAsync(CreatePromptTriggerDto dto, CancellationToken cancellationToken)
    {
        try
        {
            var validationResult = await _createValidator.ValidateAsync(dto, cancellationToken);
            if (!validationResult.IsValid)
            {
                return Result<PromptTriggerDto>.Failure(validationResult.Errors.Select(e => e.ErrorMessage).ToList());
            }

            var nameExists = await _repository.ExistsByNameAsync(dto.Name, cancellationToken);
            if (nameExists)
            {
                return Result<PromptTriggerDto>.Failure(new List<string> { $"A prompt trigger with the name '{dto.Name}' already exists" });
            }

            var promptTrigger = new PromptTrigger(
                dto.Name,
                dto.Description,
                dto.Priority,
                dto.InsightNumber,
                dto.CooldownMinutes,
                dto.DailyLimit,
                dto.TriggerCondition,
                dto.ConditionType,
                dto.AssociatedTopicIds);

            // Set condition-specific properties
            SetConditionSpecificProperties(promptTrigger, dto);

            await _repository.AddAsync(promptTrigger, cancellationToken);

            _logger.LogInformation("Prompt trigger created with ID {Id} and name {Name}", promptTrigger.Id, promptTrigger.Name);
            return Result<PromptTriggerDto>.Success(promptTrigger.ToDto());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while creating prompt trigger");
            return Result<PromptTriggerDto>.Failure(new List<string> { "An error occurred while creating the prompt trigger" });
        }
    }

    public async Task<Result<PromptTriggerDto>> UpdatePromptTriggerAsync(Guid id, UpdatePromptTriggerDto dto, CancellationToken cancellationToken)
    {
        try
        {
            var validationResult = await _updateValidator.ValidateAsync(dto, cancellationToken);
            if (!validationResult.IsValid)
            {
                return Result<PromptTriggerDto>.Failure(validationResult.Errors.Select(e => e.ErrorMessage).ToList());
            }

            var promptTrigger = await _repository.GetByIdAsync(id, cancellationToken);
            if (promptTrigger == null)
            {
                return Result<PromptTriggerDto>.Failure(new List<string> { "Prompt trigger not found" });
            }

            if (!string.IsNullOrWhiteSpace(dto.Name) && dto.Name != promptTrigger.Name)
            {
                var nameExists = await _repository.ExistsByNameAsync(dto.Name, id, cancellationToken);
                if (nameExists)
                {
                    return Result<PromptTriggerDto>.Failure(new List<string> { $"A prompt trigger with the name '{dto.Name}' already exists" });
                }
            }

            // Update basic properties
            promptTrigger.Update(
                dto.Name,
                dto.Description,
                dto.Priority,
                dto.InsightNumber,
                dto.CooldownMinutes,
                dto.DailyLimit,
                dto.TriggerCondition,
                dto.AssociatedTopicIds);

            // Update condition type if provided
            if (dto.ConditionType.HasValue && dto.ConditionType.Value != promptTrigger.ConditionType)
            {
                // When changing condition type, we need to create a new entity with the new condition type
                // This is a limitation of the current design - in a real scenario, you might want to handle this differently
                return Result<PromptTriggerDto>.Failure(new List<string> { "Changing condition type is not supported. Please create a new prompt trigger." });
            }

            // Update condition-specific properties if the condition type matches
            if (dto.ConditionType.HasValue || HasConditionSpecificUpdates(dto))
            {
                UpdateConditionSpecificProperties(promptTrigger, dto);
            }

            await _repository.UpdateAsync(promptTrigger, cancellationToken);

            _logger.LogInformation("Prompt trigger updated with ID {Id}", id);
            return Result<PromptTriggerDto>.Success(promptTrigger.ToDto());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while updating prompt trigger with ID {Id}", id);
            return Result<PromptTriggerDto>.Failure(new List<string> { "An error occurred while updating the prompt trigger" });
        }
    }

    public async Task<Result<bool>> ActivatePromptTriggerAsync(Guid id, CancellationToken cancellationToken)
    {
        try
        {
            var promptTrigger = await _repository.GetByIdAsync(id, cancellationToken);
            if (promptTrigger == null)
            {
                return Result<bool>.Failure(new List<string> { "Prompt trigger not found" });
            }

            promptTrigger.Activate();
            await _repository.UpdateAsync(promptTrigger, cancellationToken);

            _logger.LogInformation("Prompt trigger activated with ID {Id}", id);
            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while activating prompt trigger with ID {Id}", id);
            return Result<bool>.Failure(new List<string> { "An error occurred while activating the prompt trigger" });
        }
    }

    public async Task<Result<bool>> DeactivatePromptTriggerAsync(Guid id, CancellationToken cancellationToken)
    {
        try
        {
            var promptTrigger = await _repository.GetByIdAsync(id, cancellationToken);
            if (promptTrigger == null)
            {
                return Result<bool>.Failure(new List<string> { "Prompt trigger not found" });
            }

            promptTrigger.Deactivate();
            await _repository.UpdateAsync(promptTrigger, cancellationToken);

            _logger.LogInformation("Prompt trigger deactivated with ID {Id}", id);
            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while deactivating prompt trigger with ID {Id}", id);
            return Result<bool>.Failure(new List<string> { "An error occurred while deactivating the prompt trigger" });
        }
    }

    public async Task<Result<bool>> DeletePromptTriggerAsync(Guid id, CancellationToken cancellationToken)
    {
        try
        {
            var promptTrigger = await _repository.GetByIdAsync(id, cancellationToken);
            if (promptTrigger == null)
            {
                return Result<bool>.Failure(new List<string> { "Prompt trigger not found" });
            }

            await _repository.DeleteAsync(promptTrigger, cancellationToken);

            _logger.LogInformation("Prompt trigger deleted with ID {Id}", id);
            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while deleting prompt trigger with ID {Id}", id);
            return Result<bool>.Failure(new List<string> { "An error occurred while deleting the prompt trigger" });
        }
    }

    private static void SetConditionSpecificProperties(PromptTrigger promptTrigger, CreatePromptTriggerDto dto)
    {
        switch (dto.ConditionType)
        {
            case ConditionType.ScoreValue:
                if (dto.ScoreType.HasValue && dto.ScoreOperator.HasValue)
                {
                    promptTrigger.SetScoreValueCondition(dto.ScoreType.Value, dto.ScoreOperator.Value, dto.ScoreValue);
                }
                break;
            case ConditionType.TimeOfDay:
                if (dto.StartTime.HasValue && dto.EndTime.HasValue)
                {
                    promptTrigger.SetTimeOfDayCondition(dto.StartTime.Value, dto.EndTime.Value);
                }
                break;
            case ConditionType.MetricValue:
                if (dto.MetricType.HasValue && dto.MetricOperator.HasValue)
                {
                    promptTrigger.SetMetricValueCondition(dto.MetricType.Value, dto.MetricOperator.Value, dto.MetricValue);
                }
                break;
            case ConditionType.FirstSyncOfDay:
                // No additional properties needed for FirstSyncOfDay
                break;
        }
    }

    private static void UpdateConditionSpecificProperties(PromptTrigger promptTrigger, UpdatePromptTriggerDto dto)
    {
        switch (promptTrigger.ConditionType)
        {
            case ConditionType.ScoreValue:
                var scoreType = dto.ScoreType ?? promptTrigger.ScoreType;
                var scoreOperator = dto.ScoreOperator ?? promptTrigger.ScoreOperator;
                var scoreValue = dto.ScoreValue ?? promptTrigger.ScoreValue;

                if (scoreType.HasValue && scoreOperator.HasValue)
                {
                    promptTrigger.SetScoreValueCondition(scoreType.Value, scoreOperator.Value, scoreValue);
                }
                break;
            case ConditionType.TimeOfDay:
                var startTime = dto.StartTime ?? promptTrigger.StartTime;
                var endTime = dto.EndTime ?? promptTrigger.EndTime;

                if (startTime.HasValue && endTime.HasValue)
                {
                    promptTrigger.SetTimeOfDayCondition(startTime.Value, endTime.Value);
                }
                break;
            case ConditionType.MetricValue:
                var metricType = dto.MetricType ?? promptTrigger.MetricType;
                var metricOperator = dto.MetricOperator ?? promptTrigger.MetricOperator;
                var metricValue = dto.MetricValue ?? promptTrigger.MetricValue;

                if (metricType.HasValue && metricOperator.HasValue)
                {
                    promptTrigger.SetMetricValueCondition(metricType.Value, metricOperator.Value, metricValue);
                }
                break;
            case ConditionType.FirstSyncOfDay:
                // No additional properties to update for FirstSyncOfDay
                break;
        }
    }

    private static bool HasConditionSpecificUpdates(UpdatePromptTriggerDto dto)
    {
        return dto.ScoreType.HasValue ||
               dto.ScoreOperator.HasValue ||
               dto.ScoreValue != null ||
               dto.StartTime.HasValue ||
               dto.EndTime.HasValue ||
               dto.MetricType.HasValue ||
               dto.MetricOperator.HasValue ||
               dto.MetricValue != null;
    }
}

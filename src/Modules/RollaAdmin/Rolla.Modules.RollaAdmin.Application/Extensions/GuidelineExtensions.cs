using Rolla.Modules.RollaAdmin.Application.DTOs.Guidelines;
using Rolla.Modules.RollaAdmin.Core.Entities.Guidelines;

namespace Rolla.Modules.RollaAdmin.Application.Extensions;

public static class GuidelineExtensions
{
    public static GuidelineDto ToDto(this Guideline guideline)
    {
        return new GuidelineDto
        {
            Id = guideline.Id,
            Name = guideline.Name,
            Description = guideline.Description,
            PromptGuidelineContent = guideline.PromptGuidelineContent,
            IsActive = guideline.IsActive,
            CreatedAt = guideline.CreatedAt,
            UpdatedAt = guideline.UpdatedAt
        };
    }
}

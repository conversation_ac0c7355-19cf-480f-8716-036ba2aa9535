using Rolla.Modules.RollaAdmin.Application.DTOs.PromptTriggers;
using Rolla.Modules.RollaAdmin.Core.Entities.PromptTriggers;

namespace Rolla.Modules.RollaAdmin.Application.Extensions;

public static class PromptTriggerExtensions
{
    public static PromptTriggerDto ToDto(this PromptTrigger promptTrigger)
    {
        return new PromptTriggerDto
        {
            Id = promptTrigger.Id,
            Name = promptTrigger.Name,
            Description = promptTrigger.Description,
            Priority = promptTrigger.Priority,
            InsightNumber = promptTrigger.InsightNumber,
            CooldownMinutes = promptTrigger.CooldownMinutes,
            DailyLimit = promptTrigger.DailyLimit,
            IsActive = promptTrigger.IsActive,
            TriggerCondition = promptTrigger.TriggerCondition,
            ConditionType = promptTrigger.ConditionType,
            ScoreType = promptTrigger.ScoreType,
            ScoreOperator = promptTrigger.ScoreOperator,
            ScoreValue = promptTrigger.ScoreValue,
            StartTime = promptTrigger.StartTime,
            EndTime = promptTrigger.EndTime,
            MetricType = promptTrigger.MetricType,
            MetricOperator = promptTrigger.MetricOperator,
            MetricValue = promptTrigger.MetricValue,
            AssociatedTopicIds = promptTrigger.AssociatedTopicIds.ToList(),
            CreatedAt = promptTrigger.CreatedAt,
            UpdatedAt = promptTrigger.UpdatedAt
        };
    }
}

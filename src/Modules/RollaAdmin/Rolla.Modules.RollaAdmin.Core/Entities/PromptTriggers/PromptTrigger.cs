using System.Text.Json;
using Rolla.Modules.RollaAdmin.Core.Entities.PromptTriggers.Enums;

namespace Rolla.Modules.RollaAdmin.Core.Entities.PromptTriggers;

public class PromptTrigger
{
    public Guid Id { get; private set; }
    public string Name { get; private set; }
    public string? Description { get; private set; }
    public int Priority { get; private set; }
    public int InsightNumber { get; private set; }
    public int CooldownMinutes { get; private set; }
    public int DailyLimit { get; private set; }
    public bool IsActive { get; private set; }
    public TriggerCondition TriggerCondition { get; private set; }
    public ConditionType ConditionType { get; private set; }
    public ScoreType? ScoreType { get; private set; }
    public OperatorType? ScoreOperator { get; private set; }
    public string? ScoreValue { get; private set; }
    public TimeOnly? StartTime { get; private set; }
    public TimeOnly? EndTime { get; private set; }
    public MetricType? MetricType { get; private set; }
    public OperatorType? MetricOperator { get; private set; }
    public string? MetricValue { get; private set; }
    private string? _associatedTopicsJson;

    public List<Guid> AssociatedTopicIds
    {
        get => string.IsNullOrEmpty(_associatedTopicsJson)
            ? new List<Guid>()
            : JsonSerializer.Deserialize<List<Guid>>(_associatedTopicsJson) ?? new List<Guid>();
        private set => _associatedTopicsJson = value.Count == 0
            ? "[]"
            : JsonSerializer.Serialize(value);
    }

    public DateTime CreatedAt { get; private set; }
    public DateTime? UpdatedAt { get; private set; }

    protected PromptTrigger()
    {
        Name = null!;
        AssociatedTopicIds = new List<Guid>();
    }

    public PromptTrigger(
        string name,
        string? description,
        int priority,
        int insightNumber,
        int cooldownMinutes,
        int dailyLimit,
        TriggerCondition triggerCondition,
        ConditionType conditionType,
        List<Guid>? associatedTopicIds = null)
    {
        ValidateBasicProperties(name, priority, insightNumber, cooldownMinutes, dailyLimit);

        Id = Guid.NewGuid();
        Name = name;
        Description = description;
        Priority = priority;
        InsightNumber = insightNumber;
        CooldownMinutes = cooldownMinutes;
        DailyLimit = dailyLimit;
        IsActive = false;
        TriggerCondition = triggerCondition;
        ConditionType = conditionType;
        AssociatedTopicIds = associatedTopicIds ?? new List<Guid>();
        CreatedAt = DateTime.UtcNow;
        UpdatedAt = DateTime.UtcNow;
    }

    public void SetScoreValueCondition(ScoreType scoreType, OperatorType operatorType, string? value)
    {
        if (ConditionType != ConditionType.ScoreValue)
            throw new InvalidOperationException("Can only set score value condition when ConditionType is ScoreValue");

        if (operatorType == OperatorType.ChangedDuringDay && !string.IsNullOrEmpty(value))
            throw new ArgumentException("Value must be null when operator is ChangedDuringDay");

        if (operatorType != OperatorType.ChangedDuringDay && string.IsNullOrEmpty(value))
            throw new ArgumentException("Value is required when operator is not ChangedDuringDay");

        ScoreType = scoreType;
        ScoreOperator = operatorType;
        ScoreValue = value;
        UpdatedAt = DateTime.UtcNow;
    }

    public void SetTimeOfDayCondition(TimeOnly startTime, TimeOnly endTime)
    {
        if (ConditionType != ConditionType.TimeOfDay)
            throw new InvalidOperationException("Can only set time of day condition when ConditionType is TimeOfDay");

        if (startTime >= endTime)
            throw new ArgumentException("Start time must be before end time");

        StartTime = startTime;
        EndTime = endTime;
        UpdatedAt = DateTime.UtcNow;
    }

    public void SetMetricValueCondition(MetricType metricType, OperatorType operatorType, string? value)
    {
        if (ConditionType != ConditionType.MetricValue)
            throw new InvalidOperationException("Can only set metric value condition when ConditionType is MetricValue");

        if (operatorType == OperatorType.ChangedDuringDay && !string.IsNullOrEmpty(value))
            throw new ArgumentException("Value must be null when operator is ChangedDuringDay");

        if (operatorType != OperatorType.ChangedDuringDay && string.IsNullOrEmpty(value))
            throw new ArgumentException("Value is required when operator is not ChangedDuringDay");

        MetricType = metricType;
        MetricOperator = operatorType;
        MetricValue = value;
        UpdatedAt = DateTime.UtcNow;
    }

    public void Activate()
    {
        IsActive = true;
        UpdatedAt = DateTime.UtcNow;
    }

    public void Deactivate()
    {
        IsActive = false;
        UpdatedAt = DateTime.UtcNow;
    }

    public void Update(
        string? name,
        string? description,
        int? priority,
        int? insightNumber,
        int? cooldownMinutes,
        int? dailyLimit,
        TriggerCondition? triggerCondition,
        List<Guid>? associatedTopicIds)
    {
        if (!string.IsNullOrWhiteSpace(name))
            Name = name;

        if (description != null)
            Description = description;

        if (priority.HasValue)
        {
            if (priority.Value < 1)
                throw new ArgumentException("Priority must be greater than 0", nameof(priority));
            Priority = priority.Value;
        }

        if (insightNumber.HasValue)
        {
            if (insightNumber.Value < 1)
                throw new ArgumentException("Insight number must be greater than 0", nameof(insightNumber));
            InsightNumber = insightNumber.Value;
        }

        if (cooldownMinutes.HasValue)
        {
            if (cooldownMinutes.Value < 0)
                throw new ArgumentException("Cooldown minutes must be non-negative", nameof(cooldownMinutes));
            CooldownMinutes = cooldownMinutes.Value;
        }

        if (dailyLimit.HasValue)
        {
            if (dailyLimit.Value < 1)
                throw new ArgumentException("Daily limit must be greater than 0", nameof(dailyLimit));
            DailyLimit = dailyLimit.Value;
        }

        if (triggerCondition.HasValue)
            TriggerCondition = triggerCondition.Value;

        if (associatedTopicIds != null)
            AssociatedTopicIds = associatedTopicIds;

        UpdatedAt = DateTime.UtcNow;
    }

    private static void ValidateBasicProperties(string name, int priority, int insightNumber, int cooldownMinutes, int dailyLimit)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Name is required", nameof(name));

        if (priority < 1)
            throw new ArgumentException("Priority must be greater than 0", nameof(priority));

        if (insightNumber < 1)
            throw new ArgumentException("Insight number must be greater than 0", nameof(insightNumber));

        if (cooldownMinutes < 0)
            throw new ArgumentException("Cooldown minutes must be non-negative", nameof(cooldownMinutes));

        if (dailyLimit < 1)
            throw new ArgumentException("Daily limit must be greater than 0", nameof(dailyLimit));
    }
}

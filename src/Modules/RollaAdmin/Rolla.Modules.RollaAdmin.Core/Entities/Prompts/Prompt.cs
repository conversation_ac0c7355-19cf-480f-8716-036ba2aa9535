namespace Rolla.Modules.RollaAdmin.Core.Entities.Prompts;

public class Prompt
{
    public Guid Id { get; private set; }
    public string Name { get; private set; }
    public string? Description { get; private set; }
    public string SystemPromptContent { get; private set; }
    public bool IsActive { get; private set; }
    public DateTime CreatedAt { get; private set; }

    public DateTime? UpdatedAt { get; private set; }

    protected Prompt()
    {
        Name = null!;
        SystemPromptContent = null!;
    }

    public Prompt(string name, string? description, string systemPromptContent)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Name is required", nameof(name));

        if (string.IsNullOrWhiteSpace(systemPromptContent))
            throw new ArgumentException("SystemPromptContent is required", nameof(systemPromptContent));

        Id = Guid.NewGuid();
        Name = name;
        Description = description;
        SystemPromptContent = systemPromptContent;
        IsActive = false;
        CreatedAt = DateTime.UtcNow;
        UpdatedAt = DateTime.UtcNow;
    }

    public void Activate()
    {
        IsActive = true;
        UpdatedAt = DateTime.UtcNow;
    }

    public void Deactivate()
    {
        IsActive = false;
        UpdatedAt = DateTime.UtcNow;
    }

    public void Update(string? name, string? description, string? systemPromptContent)
    {
        if (!string.IsNullOrWhiteSpace(name))
            Name = name;

        if (description != null)
            Description = description;

        if (!string.IsNullOrWhiteSpace(systemPromptContent))
            SystemPromptContent = systemPromptContent;

        UpdatedAt = DateTime.UtcNow;
    }
}

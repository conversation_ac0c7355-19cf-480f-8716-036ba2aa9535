namespace Rolla.Modules.RollaAdmin.Core.Entities.Topics;

public class Topic
{
    public Guid Id { get; private set; }
    public string Name { get; private set; }
    public string? Description { get; private set; }
    public string Prompt { get; private set; }
    public int Priority { get; private set; }
    public bool IsActive { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime? UpdatedAt { get; private set; }

    protected Topic()
    {
        Name = null!;
        Prompt = null!;
    }

    public Topic(string name, string? description, string prompt, int priority)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Name is required", nameof(name));

        if (string.IsNullOrWhiteSpace(prompt))
            throw new ArgumentException("Prompt is required", nameof(prompt));

        if (priority < 0)
            throw new ArgumentException("Priority must be non-negative", nameof(priority));

        Id = Guid.NewGuid();
        Name = name;
        Description = description;
        Prompt = prompt;
        Priority = priority;
        IsActive = false;
        CreatedAt = DateTime.UtcNow;
        UpdatedAt = DateTime.UtcNow;
    }

    public void Activate()
    {
        IsActive = true;
        UpdatedAt = DateTime.UtcNow;
    }

    public void Deactivate()
    {
        IsActive = false;
        UpdatedAt = DateTime.UtcNow;
    }

    public void Update(string? name, string? description, string? prompt, int? priority)
    {
        if (!string.IsNullOrWhiteSpace(name))
            Name = name;

        if (description != null)
            Description = description;

        if (!string.IsNullOrWhiteSpace(prompt))
            Prompt = prompt;

        if (priority.HasValue && priority.Value >= 0)
            Priority = priority.Value;

        UpdatedAt = DateTime.UtcNow;
    }
}

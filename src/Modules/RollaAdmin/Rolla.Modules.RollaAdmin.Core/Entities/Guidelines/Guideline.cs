namespace Rolla.Modules.RollaAdmin.Core.Entities.Guidelines;

public class Guideline
{
    public Guid Id { get; private set; }
    public string Name { get; private set; }
    public string? Description { get; private set; }
    public string PromptGuidelineContent { get; private set; }
    public bool IsActive { get; private set; } = false;
    public DateTime CreatedAt { get; private set; }
    public DateTime? UpdatedAt { get; private set; }

    public Guideline(Guid id, string name, string? description, string promptGuidelineContent)
    {
        Id = id;
        Name = name;
        Description = description;
        PromptGuidelineContent = promptGuidelineContent;
        IsActive = false;
        CreatedAt = DateTime.UtcNow;
        UpdatedAt = DateTime.UtcNow;
    }

    private Guideline()
    {
        Name = string.Empty;
        PromptGuidelineContent = string.Empty;
    }

    public void Activate()
    {
        IsActive = true;
        UpdatedAt = DateTime.UtcNow;
    }

    public void Deactivate()
    {
        IsActive = false;
        UpdatedAt = DateTime.UtcNow;
    }

    public void Update(string? name, string? description, string? content)
    {
        if (!string.IsNullOrWhiteSpace(name))
        {
            Name = name;
        }

        if (description != null)
        {
            Description = description;
        }

        if (!string.IsNullOrWhiteSpace(content))
        {
            PromptGuidelineContent = content;
        }

        UpdatedAt = DateTime.UtcNow;
    }
}

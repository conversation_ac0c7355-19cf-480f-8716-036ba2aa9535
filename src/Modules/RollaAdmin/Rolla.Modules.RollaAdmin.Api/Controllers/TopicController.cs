using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Rolla.BuildingBlocks.Application.Controllers;
using Rolla.Modules.RollaAdmin.Api.Contracts.Topics;
using Rolla.Modules.RollaAdmin.Application.DTOs.Topics;
using Rolla.Modules.RollaAdmin.Application.Interfaces.Topics;
using Rolla.Modules.RollaAdmin.Core.Models;
using Microsoft.AspNetCore.Mvc;

namespace Rolla.Modules.RollaAdmin.Api.Controllers;

/// <summary>
/// Manages topics with comprehensive CRUD operations, search, filtering, and activation features.
/// </summary>
[ApiController]
[Route("api/rolla-admin/topics")]
[Produces("application/json")]
[Tags("Rolla Admin - Topic Management System - Topics")]
public class TopicController : BaseController
{
    private readonly ITopicService _topicService;

    public TopicController(ILogger<TopicController> logger, ITopicService topicService)
        : base(logger)
    {
        _topicService = topicService;
    }

    /// <summary>
    /// Retrieve all topics with advanced filtering, search, and pagination
    /// </summary>
    /// <remarks>
    /// Supports comprehensive filtering and search capabilities:
    ///
    /// <para><strong>Search Features:</strong></para>
    /// - Full-text search across name, description, and prompt content
    /// - Case-insensitive matching with partial word support
    /// - Automatic wildcard matching for flexible queries
    ///
    /// <para><strong>Filter Options:</strong></para>
    /// - **IsActive**: Filter by activation status (true/false/null for all)
    /// - **Priority Range**: Filter by minimum and/or maximum priority values
    /// - **Date Ranges**: Filter by creation and update timestamps
    /// - **Sorting**: Multiple sort fields with ascending/descending options
    ///
    /// <para><strong>Pagination:</strong></para>
    /// - Configurable page size (1-100 items per page)
    /// - Efficient offset-based pagination
    /// - Total count and page metadata included
    ///
    /// <para><strong>Performance Notes:</strong></para>
    /// - Results are cached for optimal performance
    /// - Database indexes optimize common filter combinations
    /// - Large result sets automatically paginated
    /// </remarks>
    /// <param name="searchTerm">Search term for name, description, or prompt content</param>
    /// <param name="isActive">Filter by activation status (optional)</param>
    /// <param name="minPriority">Minimum priority value (optional)</param>
    /// <param name="maxPriority">Maximum priority value (optional)</param>
    /// <param name="createdAfter">Filter topics created after this date (optional)</param>
    /// <param name="createdBefore">Filter topics created before this date (optional)</param>
    /// <param name="updatedAfter">Filter topics updated after this date (optional)</param>
    /// <param name="updatedBefore">Filter topics updated before this date (optional)</param>
    /// <param name="sortBy">Sort field: Name, Description, Priority, IsActive, CreatedAt, UpdatedAt (default: CreatedAt)</param>
    /// <param name="sortDescending">Sort direction: true for descending, false for ascending (default: true)</param>
    /// <param name="pageNumber">Page number (1-based, default: 1)</param>
    /// <param name="pageSize">Items per page (1-100, default: 10)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paginated list of topics matching the specified criteria</returns>
    [HttpGet]
    [ProducesResponseType(typeof(PagedResult<TopicDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetAllTopics(
        [FromQuery] string? searchTerm = null,
        [FromQuery] bool? isActive = null,
        [FromQuery] int? minPriority = null,
        [FromQuery] int? maxPriority = null,
        [FromQuery] DateTime? createdAfter = null,
        [FromQuery] DateTime? createdBefore = null,
        [FromQuery] DateTime? updatedAfter = null,
        [FromQuery] DateTime? updatedBefore = null,
        [FromQuery] string? sortBy = "CreatedAt",
        [FromQuery] bool sortDescending = true,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        CancellationToken cancellationToken = default)
    {
        var request = new TopicFilterRequest
        {
            SearchTerm = searchTerm,
            IsActive = isActive,
            MinPriority = minPriority,
            MaxPriority = maxPriority,
            CreatedAfter = createdAfter,
            CreatedBefore = createdBefore,
            UpdatedAfter = updatedAfter,
            UpdatedBefore = updatedBefore,
            SortBy = sortBy,
            SortDescending = sortDescending,
            PageNumber = pageNumber,
            PageSize = pageSize
        };

        return await ExecuteAsync(
            () => _topicService.GetAllTopicsAsync(request, cancellationToken),
            "Topics retrieved successfully",
            "Failed to retrieve topics"
        );
    }

    /// <summary>
    /// Retrieve a specific topic by its unique identifier
    /// </summary>
    /// <remarks>
    /// Returns complete topic details including:
    /// - Basic information (name, description, prompt)
    /// - Priority and activation status
    /// - Creation and modification timestamps
    /// - Associated metadata
    ///
    /// <para><strong>Use Cases:</strong></para>
    /// - Topic detail views in admin interfaces
    /// - Pre-populating edit forms
    /// - API integrations requiring full topic data
    /// - Audit and compliance reporting
    /// </remarks>
    /// <param name="id">Unique topic identifier (GUID format)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Complete topic details if found</returns>
    [HttpGet("{id:guid}")]
    [ProducesResponseType(typeof(TopicDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetTopicById(Guid id, CancellationToken cancellationToken)
    {
        return await ExecuteAsync(
            () => _topicService.GetTopicByIdAsync(id, cancellationToken),
            "Topic retrieved successfully",
            $"Failed to retrieve topic {id}"
        );
    }

    /// <summary>
    /// Retrieve all currently active topics
    /// </summary>
    /// <remarks>
    /// Returns topics that are currently active, ordered by priority (ascending) then by name.
    /// This endpoint is optimized for scenarios where you need to work with active topics only.
    ///
    /// <para><strong>Ordering:</strong></para>
    /// - Primary: Priority (ascending - lower numbers first)
    /// - Secondary: Name (alphabetical)
    ///
    /// <para><strong>Use Cases:</strong></para>
    /// - Displaying active topics in user interfaces
    /// - API integrations that only work with active content
    /// - Performance-optimized queries for active data
    /// </remarks>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of all active topics ordered by priority</returns>
    [HttpGet("active")]
    [ProducesResponseType(typeof(List<TopicDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetActiveTopics(CancellationToken cancellationToken)
    {
        return await ExecuteAsync(
            () => _topicService.GetActiveTopicsAsync(cancellationToken),
            "Active topics retrieved successfully",
            "Failed to retrieve active topics"
        );
    }

    /// <summary>
    /// Create a new topic
    /// </summary>
    /// <remarks>
    /// Creates a new topic with the specified details. The topic will be created in an inactive state
    /// and must be explicitly activated using the activation endpoint.
    ///
    /// <para><strong>Validation Rules:</strong></para>
    /// - Name: Required, maximum 100 characters, must be unique
    /// - Description: Optional, maximum 500 characters
    /// - Prompt: Required, maximum 5000 characters
    /// - Priority: Required, must be between 0 and 1000
    ///
    /// <para><strong>Business Rules:</strong></para>
    /// - Topic names must be unique across all topics
    /// - New topics are created in inactive state
    /// - Creation timestamp automatically set to current UTC time
    /// - Priority determines ordering when multiple topics are active
    ///
    /// <para><strong>Common Use Cases:</strong></para>
    /// - Adding new conversation topics to the system
    /// - Importing topics from external sources
    /// - Creating topic templates for different scenarios
    /// </remarks>
    /// <param name="request">Topic creation details (name, description, prompt, priority)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The newly created topic with ID and timestamps</returns>
    [HttpPost]
    [ProducesResponseType(typeof(TopicDto), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status409Conflict)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> CreateTopic([FromBody] CreateTopicRequest request, CancellationToken cancellationToken)
    {
        var dto = new CreateTopicDto
        {
            Name = request.Name,
            Description = request.Description,
            Prompt = request.Prompt,
            Priority = request.Priority
        };

        return await ExecuteAsync(
            () => _topicService.CreateTopicAsync(dto, cancellationToken),
            "Topic created successfully",
            "Failed to create topic"
        );
    }

    /// <summary>
    /// Update an existing topic
    /// </summary>
    /// <remarks>
    /// Updates one or more fields of an existing topic. Only provided fields will be updated;
    /// null or missing fields will remain unchanged.
    ///
    /// <para><strong>Partial Update Support:</strong></para>
    /// - Any combination of fields can be updated
    /// - Null values are ignored (field remains unchanged)
    /// - Empty strings are treated as valid updates
    /// - At least one field must be provided
    ///
    /// <para><strong>Validation Rules:</strong></para>
    /// - Name: If provided, maximum 100 characters and must be unique
    /// - Description: If provided, maximum 500 characters
    /// - Prompt: If provided, maximum 5000 characters
    /// - Priority: If provided, must be between 0 and 1000
    ///
    /// <para><strong>Business Rules:</strong></para>
    /// - Topic names must remain unique across all topics
    /// - UpdatedAt timestamp automatically set to current UTC time
    /// - Activation status is not affected by updates
    /// - Priority changes affect ordering of active topics
    /// </remarks>
    /// <param name="id">Unique topic identifier (GUID format)</param>
    /// <param name="request">Topic update details (all fields optional)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The updated topic with new values and timestamp</returns>
    [HttpPut("{id:guid}")]
    [ProducesResponseType(typeof(TopicDto), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status409Conflict)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> UpdateTopic(Guid id, [FromBody] UpdateTopicRequest request, CancellationToken cancellationToken)
    {
        var dto = new UpdateTopicDto
        {
            Name = request.Name,
            Description = request.Description,
            Prompt = request.Prompt,
            Priority = request.Priority
        };

        return await ExecuteAsync(
            () => _topicService.UpdateTopicAsync(id, dto, cancellationToken),
            "Topic updated successfully",
            $"Failed to update topic {id}"
        );
    }

    /// <summary>
    /// Activate a topic
    /// </summary>
    /// <remarks>
    /// Activates the specified topic, making it available for use. Unlike prompts and guidelines,
    /// multiple topics can be active simultaneously, allowing for diverse conversation scenarios.
    ///
    /// <para><strong>Activation Rules:</strong></para>
    /// - Multiple topics can be active at the same time
    /// - Active topics are ordered by priority (lower numbers first)
    /// - UpdatedAt timestamp set when activation occurs
    /// - Operation is idempotent (activating an active topic succeeds)
    ///
    /// <para><strong>Common Use Cases:</strong></para>
    /// - Enabling topics for production use
    /// - A/B testing different topic configurations
    /// - Seasonal or event-based topic activation
    /// - Gradual rollout of new conversation topics
    /// </remarks>
    /// <param name="id">Unique topic identifier (GUID format)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Confirmation of successful activation</returns>
    [HttpPost("{id:guid}/activate")]
    [ProducesResponseType(typeof(bool), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> ActivateTopic(Guid id, CancellationToken cancellationToken)
    {
        return await ExecuteAsync(
            () => _topicService.ActivateTopicAsync(id, cancellationToken),
            "Topic activated successfully",
            $"Failed to activate topic {id}"
        );
    }

    /// <summary>
    /// Deactivate a topic
    /// </summary>
    /// <remarks>
    /// Deactivates the specified topic, removing it from active use while preserving
    /// the topic data for potential future reactivation.
    ///
    /// <para><strong>Deactivation Rules:</strong></para>
    /// - Topic becomes unavailable for new conversations
    /// - Existing conversations using the topic are not affected
    /// - UpdatedAt timestamp set when deactivation occurs
    /// - Operation is idempotent (deactivating an inactive topic succeeds)
    ///
    /// <para><strong>Common Use Cases:</strong></para>
    /// - Temporarily disabling problematic topics
    /// - Seasonal topic management
    /// - A/B testing conclusion
    /// - Maintenance and content review periods
    /// </remarks>
    /// <param name="id">Unique topic identifier (GUID format)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Confirmation of successful deactivation</returns>
    [HttpPost("{id:guid}/deactivate")]
    [ProducesResponseType(typeof(bool), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> DeactivateTopic(Guid id, CancellationToken cancellationToken)
    {
        return await ExecuteAsync(
            () => _topicService.DeactivateTopicAsync(id, cancellationToken),
            "Topic deactivated successfully",
            $"Failed to deactivate topic {id}"
        );
    }

    /// <summary>
    /// Delete a topic permanently
    /// </summary>
    /// <remarks>
    /// Permanently removes a topic from the system. This operation cannot be undone.
    ///
    /// <para><strong>⚠️ Warning: Destructive Operation</strong></para>
    /// This action permanently deletes the topic and cannot be reversed. Consider
    /// deactivating the topic instead if you might need it in the future.
    ///
    /// <para><strong>Deletion Rules:</strong></para>
    /// - Topic is permanently removed from the database
    /// - Operation cannot be undone
    /// - Associated triggers (when implemented) may also be affected
    /// - Active topics can be deleted (consider deactivating first)
    ///
    /// <para><strong>Best Practices:</strong></para>
    /// - Deactivate topic first to test impact
    /// - Backup topic data before deletion if needed
    /// - Verify no critical dependencies exist
    /// - Consider archiving instead of deletion for audit purposes
    ///
    /// <para><strong>Use Cases:</strong></para>
    /// - Removing test or duplicate topics
    /// - Cleaning up obsolete content
    /// - Compliance with data retention policies
    /// - System maintenance and optimization
    /// </remarks>
    /// <param name="id">Unique topic identifier (GUID format)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Confirmation of successful deletion</returns>
    [HttpDelete("{id:guid}")]
    [ProducesResponseType(typeof(bool), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> DeleteTopic(Guid id, CancellationToken cancellationToken)
    {
        return await ExecuteAsync(
            () => _topicService.DeleteTopicAsync(id, cancellationToken),
            "Topic deleted successfully",
            $"Failed to delete topic {id}"
        );
    }
}

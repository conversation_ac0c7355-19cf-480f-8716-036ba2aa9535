using System.ComponentModel.DataAnnotations;
using System.Reflection;

namespace Rolla.Modules.RollaAdmin.Api.Validation;

/// <summary>
/// Validation attribute that ensures at least one property has a non-null value.
/// </summary>
public class AtLeastOneFieldAttribute : ValidationAttribute
{
    public override bool IsValid(object? value)
    {
        if (value == null)
            return false;

        var properties = value.GetType().GetProperties(BindingFlags.Public | BindingFlags.Instance);
        
        return properties.Any(property =>
        {
            var propertyValue = property.GetValue(value);
            
            // Check if property has a value
            return propertyValue switch
            {
                null => false,
                string str => !string.IsNullOrWhiteSpace(str),
                _ => true
            };
        });
    }

    public override string FormatErrorMessage(string name)
    {
        return ErrorMessage ?? "At least one field must be provided for update.";
    }
}

using OpenSearch.Client;
using Rolla.BuildingBlocks.Application.Interfaces;
using Rolla.Modules.Health.Core.Entities;
using Rolla.Modules.Health.Core.Repositories;
using Rolla.Modules.Health.Core.ValueObjects;

namespace Rolla.Modules.Health.Infrastructure.Repositories
{
    public class HeartRateRepository(IOpenSearchClientProvider provider) : IHeartRateRepository
    {
        private readonly IOpenSearchClient _client = provider.GetClient();
        private const string DataStreamName = "health_datastream";
        private const string HealthMetricName = "heart_rate";

        public async Task<HeartRateProjection> GetHeartRateDataAsync(string userId, long fromEpoch, long toEpoch, string timezone,
            HealthMetricAggregationType aggregationType, HealthSourceType source)
        {
            return aggregationType.Value switch
            {
                "hourly" => await GetHourlyDataAsync(userId, fromEpoch, toEpoch, timezone, source),
                "daily" => await GetDailyDataAsync(userId, fromEpoch, toEpoch, timezone, source),
                "monthly" => await GetMonthlyDataAsync(userId, fromEpoch, toEpoch, timezone, source),
                _ => throw new ArgumentException($"Invalid aggregation type: {aggregationType}")
            };
        }

        private async Task<HeartRateProjection> GetHourlyDataAsync(string userId, long fromEpoch, long toEpoch, string timezone, HealthSourceType source)
        {
            var fromDate = DateTimeOffset.FromUnixTimeMilliseconds(fromEpoch).UtcDateTime;
            var toDate = DateTimeOffset.FromUnixTimeMilliseconds(toEpoch).UtcDateTime;

            var searchResponse = await _client.SearchAsync<HealthMetric>(s => s
            .Index(DataStreamName)
            .Size(0)
            .Query(q => q
                .Bool(b => b
                    .Must(
                        m => m.Term(t => t.Field("user_id").Value(userId)),
                        m => m.Range(r => r
                            .Field("timestamp")
                            .GreaterThanOrEquals(fromEpoch)
                            .LessThanOrEquals(toEpoch)
                        )
                    )
                )
            )
            .Aggregations(aggs => aggs
                .DateHistogram("minute_buckets", dh => dh
                    .Field("timestamp")
                    .FixedInterval(new Time(TimeSpan.FromMinutes(10)))
                    .MinimumDocumentCount(0)
                    .ExtendedBounds(
                        DateMath.Anchored(fromDate),
                        DateMath.Anchored(toDate)
                    )
                    .TimeZone(timezone)
                    .Aggregations(innerAggs => innerAggs
                        .Average("avg_heart_rate", avg => avg.Field("heart_rate"))
                        .Max("max_heart_rate", max => max.Field("heart_rate"))
                            .MovingAverage("moving_average", ma => ma
                            .BucketsPath("avg_heart_rate")
                            .Window(1)
                            .Model(m => m.Simple())
                        )
                    )
                )
                .MinBucket("resting_hr", mb => mb
                    .BucketsPath("minute_buckets>moving_average")
                )
                .MaxBucket("overall_max", mb => mb
                    .BucketsPath("minute_buckets>max_heart_rate")
                )
            )
        );

            if (!searchResponse.IsValid)
            {
                throw new Exception($"Failed to get hourly heart rate data: {searchResponse.DebugInformation}");
            }

            var items = new List<HeartRateDataItem>();
            var minuteBuckets = searchResponse.Aggregations.DateHistogram("minute_buckets");

            foreach (var bucket in minuteBuckets.Buckets)
            {
                var avgHeartRate = bucket.Average("avg_heart_rate");
                items.Add(new HeartRateDataItem
                {
                    Time = bucket.KeyAsString,
                    Source = source.ToString(),
                    Data = avgHeartRate?.Value != null ? new HeartRateData
                    {
                        High = (int)Math.Round(avgHeartRate.Value.Value),
                        Resting = null
                    } : null
                });
            }

            var overallMax = searchResponse.Aggregations.MaxBucket("overall_max");
            var restingHr = searchResponse.Aggregations.MinBucket("resting_hr");

            var restingValue = (int)Math.Round(restingHr?.Value ?? 0);

            return new HeartRateProjection
            {
                Items = items,
                Overall = new HeartRateData()
                {
                    High = (float?)overallMax?.Value,
                    Resting = restingValue
                },
                AvgHeartRateScore = 0,
                Autogoal = 0
            };
        }

        private async Task<HeartRateProjection> GetDailyDataAsync(string userId, long fromEpoch, long toEpoch, string timezone, HealthSourceType source)
        {
            throw await Task.FromResult(new NotImplementedException());
        }

        private async Task<HeartRateProjection> GetMonthlyDataAsync(string userId, long fromEpoch, long toEpoch, string timezone, HealthSourceType source)
        {
            throw await Task.FromResult(new NotImplementedException());
        }
    }
}

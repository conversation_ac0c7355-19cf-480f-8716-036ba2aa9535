using Rolla.Modules.Health.Core.Repositories;
using OpenSearch.Client;
using Rolla.BuildingBlocks.Application.Interfaces;
using Rolla.Modules.Health.Core.Entities;

namespace Rolla.Modules.Health.Infrastructure.Repositories;

public class HealthMetricRepository(IOpenSearchClientProvider provider) : IHealthMetricRepository
{
    private readonly IOpenSearchClient _client = provider.GetClient();
    private const string DataStreamName = "health_datastream";

    public async Task<HealthMetric?> GetLatestByUserIdAsync(string userId)
    {
        var response = await _client.SearchAsync<HealthMetric>(s => s
            .Index(DataStreamName)
            .Query(q => q
                .Term(t => t
                    .Field(f => f.UserId)
                    .Value(userId)
                )
            )
            .Sort(sort => sort
                .Descending(f => f.Timestamp)
            )
            .Size(1)
        );

        if (!response.IsValid)
        {
            throw new Exception($"Failed to search health data: {response.DebugInformation}");
        }

        return response.Documents.FirstOrDefault();
    }
}

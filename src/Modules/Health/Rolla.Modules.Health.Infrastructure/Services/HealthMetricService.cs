using Rolla.Modules.Health.Application.Contracts.Request;
using Rolla.Modules.Health.Application.Services;
using Rolla.Modules.Health.Core.Repositories;
using Rolla.Modules.Health.Core.ValueObjects;

namespace Rolla.Modules.Health.Infrastructure.Services;

public class HealthMetricService(IHeartRateRepository heartRateRepository) : IHealthMetricService
{
    private readonly IHeartRateRepository _heartRateRepository = heartRateRepository;

    public async Task<HeartRateProjection> GetHeartRateDataAsync(string userId, HealthMetricRequest request)
    {
        //var timezone = await _userService.GetUserTimezoneAsync(userId);
        var timezone = "UTC";
        //var autogoal = await _baselineService.GetAutogoalAsync(userId, request.To, "rhr");

        var fromEpoch = new DateTimeOffset(request.From).ToUnixTimeMilliseconds();
        var toEpoch = new DateTimeOffset(request.To).ToUnixTimeMilliseconds();

        var aggregationType = request.AggregationType
                              ?? throw new ArgumentNullException(nameof(request.AggregationType));

        var sourceType = request.Source
                         ?? throw new ArgumentNullException(nameof(request.Source));

        // Get data from repository
        var response = await _heartRateRepository.GetHeartRateDataAsync(
            userId, fromEpoch, toEpoch, timezone, request.AggregationType, request.Source);

        // Calculate scores
        /*if (request.Type != HeartRateAggregationType.Hourly)
        {
            var scores = await _scoringService.GetHeartRateScoresAsync(
                userId, request.From, request.To, request.Type);
            response.HeartRateScores = scores.Scores;
            response.AvgHeartRateScore = scores.Average;
        }
        else if (response.Overall.Resting.HasValue)
        {
            response.AvgHeartRateScore = await _scoringService.CalculateDailyScoreAsync(
                (int)response.Overall.Resting.Value, userId, request.To);
        }

        response.Autogoal = autogoal;*/

        return response;
    }
}

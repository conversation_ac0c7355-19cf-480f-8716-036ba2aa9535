using Rolla.Modules.Health.Application.Contracts.Enums;
using CoreAggregationType = Rolla.Modules.Health.Core.ValueObjects.HealthMetricAggregationType;
using CoreSourceType = Rolla.Modules.Health.Core.ValueObjects.HealthSourceType;

namespace Rolla.Modules.Health.Application.Mappings;

public static class EnumMappingExtensions
{
    public static CoreAggregationType ToCoreValueObject(this HealthMetricAggregationType aggregationType)
    {
        return aggregationType switch
        {
            HealthMetricAggregationType.Hourly => CoreAggregationType.Hourly,
            HealthMetricAggregationType.Daily => CoreAggregationType.Daily,
            HealthMetricAggregationType.Monthly => CoreAggregationType.Monthly,
            _ => throw new ArgumentOutOfRangeException(nameof(aggregationType))
        };
    }

    public static CoreSourceType ToCoreValueObject(this HealthSourceType sourceType)
    {
        return sourceType switch
        {
            HealthSourceType.Rolla => CoreSourceType.Rolla,
            HealthSourceType.Garmin => CoreSourceType.Garmin,
            HealthSourceType.Apple => CoreSourceType.Apple,
            _ => throw new ArgumentOutOfRangeException(nameof(sourceType))
        };
    }

    public static HealthMetricAggregationType ToApiEnum(this CoreAggregationType coreType)
    {
        if (coreType == CoreAggregationType.Hourly) return HealthMetricAggregationType.Hourly;
        if (coreType == CoreAggregationType.Daily) return HealthMetricAggregationType.Daily;
        if (coreType == CoreAggregationType.Monthly) return HealthMetricAggregationType.Monthly;

        throw new ArgumentException($"Unknown core aggregation type: {coreType}");
    }

    public static HealthSourceType ToApiEnum(this CoreSourceType coreType)
    {
        if (coreType == CoreSourceType.Rolla) return HealthSourceType.Rolla;
        if (coreType == CoreSourceType.Garmin) return HealthSourceType.Garmin;
        if (coreType == CoreSourceType.Apple) return HealthSourceType.Apple;

        throw new ArgumentException($"Unknown core source type: {coreType}");
    }
}

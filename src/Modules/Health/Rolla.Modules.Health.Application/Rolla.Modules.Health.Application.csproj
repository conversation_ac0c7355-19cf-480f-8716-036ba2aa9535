<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Options" Version="9.0.0"/>
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.0"/>
    <PackageReference Include="Scrutor" Version="5.0.1"/>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Rolla.Modules.Health.Core\Rolla.Modules.Health.Core.csproj"/>
    <ProjectReference Include="..\..\..\BuildingBlocks\Rolla.BuildingBlocks.Application\Rolla.BuildingBlocks.Application.csproj"/>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Contracts\Response\" />
  </ItemGroup>

</Project>

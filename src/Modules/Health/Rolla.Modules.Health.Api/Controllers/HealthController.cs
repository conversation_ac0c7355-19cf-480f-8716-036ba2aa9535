using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Rolla.Modules.Health.Api.Contracts.Request;
using Rolla.Modules.Health.Application.Contracts.Request;
using Rolla.Modules.Health.Application.Mappings;
using Rolla.Modules.Health.Application.Services;
using Rolla.Modules.Health.Core.ValueObjects;

namespace Rolla.Modules.Health.Api.Controllers;

[ApiController]
[Route("api/health")]
[ApiExplorerSettings(IgnoreApi = true)]
public class HealthController : ControllerBase
{
    private readonly IHealthMetricService _healthMetricService;
    private readonly ILogger<HealthController> _logger;

    public HealthController(
        IHealthMetricService healthMetricService,
        ILogger<HealthController> logger)
    {
        _healthMetricService = healthMetricService;
        _logger = logger;
    }

    [HttpGet("{userId}/heart-rate")]
    [ProducesResponseType(typeof(IEnumerable<HeartRateProjection>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetHeartRateData(
        [FromRoute] string userId,
        [FromQuery] GetHeartRateDataRequest request)
    {
        try
        {
            var validationResult = request.Validate();
            if (!validationResult.IsValid)
            {
                return BadRequest(new { errors = validationResult.Errors });
            }

            var serviceRequest = new HealthMetricRequest
            {
                From = request.From,
                To = request.To,
                AggregationType = request.AggregationType!.Value.ToCoreValueObject(),
                Source = request.Source!.Value.ToCoreValueObject()
            };

            var result = await _healthMetricService.GetHeartRateDataAsync(userId, serviceRequest);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting heart rate data for user {UserId}", userId);
            return StatusCode(500, new { error = "An error occurred while processing your request." });
        }
    }
}

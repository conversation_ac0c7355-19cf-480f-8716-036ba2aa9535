using Rolla.Modules.Health.Application.Contracts.Enums;
namespace Rolla.Modules.Health.Api.Contracts.Request;

public record GetHeartRateDataRequest
{
    public DateTime From { get; init; }
    public DateTime To { get; init; }
    public HealthMetricAggregationType? AggregationType { get; init; }
    public HealthSourceType? Source { get; init; }

    public ValidationResult Validate()
    {
        var errors = new List<string>();

        if (From >= To)
        {
            errors.Add("Invalid date range. 'from' must be before 'to'.");
        }

        if (From > DateTime.UtcNow)
        {
            errors.Add("'from' date cannot be in the future.");
        }

        if (!AggregationType.HasValue)
        {
            errors.Add("AggregationType is required.");
        }

        if (!Source.HasValue)
        {
            errors.Add("Source is required.");
        }

        return new ValidationResult(errors);
    }
}

public record ValidationResult(List<string> Errors)
{
    public bool IsValid => !Errors.Any();
}

using Microsoft.AspNetCore.Builder;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Rolla.BuildingBlocks.Application.Modules;
using Rolla.Modules.Health.Application.Configuration;
using Rolla.Modules.Health.Application.Services;
using Rolla.Modules.Health.Core.Repositories;
using Rolla.Modules.Health.Infrastructure.Database;
using Rolla.Modules.Health.Infrastructure.Repositories;
using Rolla.Modules.Health.Infrastructure.Services;

namespace Rolla.Modules.Health.Api.Module;

public class HealthModule : ModuleBase
{
    public override void RegisterModule(IServiceCollection services, IConfiguration configuration)
    {
        services.Configure<HealthOptions>(configuration.GetSection(HealthOptions.SectionName));

        services.AddDbContext<HealthDbContext>(options =>
            options.UseMySql(
                configuration.GetConnectionString("DefaultConnection"),
                ServerVersion.AutoDetect(configuration.GetConnectionString("DefaultConnection"))));

        services.AddScoped<IHeartRateRepository, HeartRateRepository>();
        services.AddScoped<IHealthMetricService, HealthMetricService>();

    }

    public override void UseModule(IApplicationBuilder app)
    {
    }

}

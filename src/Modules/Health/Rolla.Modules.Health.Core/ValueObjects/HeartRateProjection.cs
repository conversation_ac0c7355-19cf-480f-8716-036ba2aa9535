namespace Rolla.Modules.Health.Core.ValueObjects;

public class HeartRateProjection
{
    public List<HeartRateDataItem> Items { get; set; } = new();
    public HeartRateData Overall { get; set; } = new();
    public Dictionary<string, float>? HeartRateScores { get; set; }
    public float? AvgHeartRateScore { get; set; }
    public float? Autogoal { get; set; }
}

public class HeartRateData
{
    public float? High { get; set; }
    public int? Resting { get; set; }
}

public class HeartRateDataItem
{
    public string Time { get; set; } = string.Empty;
    public string Source { get; set; } = string.Empty;
    public HeartRateData? Data { get; set; }
}

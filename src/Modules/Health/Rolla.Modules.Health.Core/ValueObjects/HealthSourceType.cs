namespace Rolla.Modules.Health.Core.ValueObjects;
public sealed class HealthSourceType : IEquatable<HealthSourceType>
{
    public static readonly HealthSourceType Rolla = new(nameof(Rolla), "rolla");
    public static readonly HealthSourceType Garmin = new(nameof(Garmin), "garmin");
    public static readonly HealthSourceType Apple = new(nameof(Apple), "apple");

    public string Name { get; }
    public string Value { get; }

    private HealthSourceType(string name, string value)
    {
        Name = name;
        Value = value;
    }

    public static IEnumerable<HealthSourceType> List() => new[] { Rolla, Garmin, Apple };

    public static HealthSourceType FromValue(string value)
    {
        foreach (var item in List())
        {
            if (string.Equals(item.Value, value, StringComparison.OrdinalIgnoreCase))
                return item;
        }

        throw new ArgumentException($"Invalid HealthSourceType value: {value}");
    }

    public override string ToString() => Value;

    public override bool Equals(object? obj) => Equals(obj as HealthSourceType);

    public bool Equals(HealthSourceType? other) => other != null && Value == other.Value;

    public override int GetHashCode() => Value.GetHashCode();

    public static bool operator ==(HealthSourceType? left, HealthSourceType? right)
        => EqualityComparer<HealthSourceType>.Default.Equals(left, right);

    public static bool operator !=(HealthSourceType? left, HealthSourceType? right)
        => !(left == right);
}

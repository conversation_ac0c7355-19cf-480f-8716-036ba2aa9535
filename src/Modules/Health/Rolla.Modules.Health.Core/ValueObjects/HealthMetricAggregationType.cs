namespace Rolla.Modules.Health.Core.ValueObjects;
public sealed class HealthMetricAggregationType : IEquatable<HealthMetricAggregationType>
{

    public static readonly HealthMetricAggregationType Hourly = new(nameof(Hourly), "hourly");
    public static readonly HealthMetricAggregationType Daily = new(nameof(Daily), "daily");
    public static readonly HealthMetricAggregationType Monthly = new(nameof(Monthly), "monthly");

    public string Name { get; }
    public string Value { get; }

    private HealthMetricAggregationType(string name, string value)
    {
        Name = name;
        Value = value;
    }

    public static IEnumerable<HealthMetricAggregationType> List() => new[] { Hourly, Daily, Monthly };

    public static HealthMetricAggregationType FromValue(string value)
    {
        foreach (var item in List())
        {
            if (string.Equals(item.Value, value, StringComparison.OrdinalIgnoreCase))
                return item;
        }

        throw new ArgumentException($"Invalid HealthMetricAggregationType value: {value}");
    }

    public override string ToString() => Value;

    public override bool Equals(object? obj) => Equals(obj as HealthMetricAggregationType);

    public bool Equals(HealthMetricAggregationType? other)
        => other is not null && Value == other.Value;

    public override int GetHashCode() => Value.GetHashCode();

    public static bool operator ==(HealthMetricAggregationType? left, HealthMetricAggregationType? right)
        => EqualityComparer<HealthMetricAggregationType>.Default.Equals(left, right);

    public static bool operator !=(HealthMetricAggregationType? left, HealthMetricAggregationType? right)
        => !(left == right);
}

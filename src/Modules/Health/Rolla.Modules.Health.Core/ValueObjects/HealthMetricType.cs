namespace Rolla.Modules.Health.Core.ValueObjects;

public sealed class HealthMetricType : IEquatable<HealthMetricType>
{
    public static readonly HealthMetricType HeartRate = new(nameof(HeartRate), "heart_rate");
    public static readonly HealthMetricType Hrv = new(nameof(Hrv), "hrv");
    public static readonly HealthMetricType Calories = new(nameof(Calories), "calories");
    public static readonly HealthMetricType Steps = new(nameof(Steps), "steps");

    public string Name { get; }
    public string Value { get; }

    private HealthMetricType(string name, string value)
    {
        Name = name;
        Value = value;
    }

    public static IEnumerable<HealthMetricType> List() => new[] { HeartRate, Hrv, Calories, Steps };

    public static HealthMetricType FromValue(string value)
    {
        foreach (var item in List())
        {
            if (string.Equals(item.Value, value, StringComparison.OrdinalIgnoreCase))
                return item;
        }

        throw new ArgumentException($"Invalid HealthMetricType value: {value}");
    }

    public override string ToString() => Value;

    public override bool Equals(object? obj) => Equals(obj as HealthMetricType);

    public bool Equals(HealthMetricType? other) => other != null && Value == other.Value;

    public override int GetHashCode() => Value.GetHashCode();

    public static bool operator ==(HealthMetricType? left, HealthMetricType? right)
        => EqualityComparer<HealthMetricType>.Default.Equals(left, right);

    public static bool operator !=(HealthMetricType? left, HealthMetricType? right)
        => !(left == right);
}

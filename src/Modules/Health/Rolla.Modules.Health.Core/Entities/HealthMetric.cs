using OpenSearch.Client;

namespace Rolla.Modules.Health.Core.Entities;

[OpenSearchType(RelationName = "health_datastream")]
public class HealthMetric
{
    [Number(NumberType.Float, Name = "calories")]
    public float Calories { get; set; }

    [Number(NumberType.Short, Name = "heart_rate")]
    public short HeartRate { get; set; }

    [Number(NumberType.Short, Name = "hrv")]
    public short Hrv { get; set; }

    [Keyword(Name = "source")] public string Source { get; set; } = string.Empty;

    [Number(NumberType.Float, Name = "steps")]
    public float Steps { get; set; }

    [Number(NumberType.Long, Name = "timestamp")]
    public long Timestamp { get; set; }

    [Keyword(Name = "user_id")] public string UserId { get; set; } = string.Empty;
}
